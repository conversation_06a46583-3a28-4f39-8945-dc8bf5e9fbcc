
import React, { createContext, useState, useEffect, ReactNode, useCallback } from 'react';
import { LiveData, SeaStateType, SailPlan } from '../types';
import { SAIL_OPTIONS } from '../constants';
import { fetchCurrentSailPlan, updateSailPlan as apiUpdateSailPlan } from '../services/api';

interface LiveDataContextType {
  liveData: LiveData | null;
  sailPlan: SailPlan;
  updateSailPlan: (newPlan: SailPlan) => Promise<void>;
  loadingSailPlan: boolean;
  errorSailPlan: string | null;
}

export const LiveDataContext = createContext<LiveDataContextType>({
  liveData: null,
  sailPlan: {
    headsail: SAIL_OPTIONS.HEADSAILS[0],
    spinnaker: SAIL_OPTIONS.SPINNAKERS[0],
    reef: SAIL_OPTIONS.REEFS[0],
  },
  updateSailPlan: async () => {},
  loadingSailPlan: true,
  errorSailPlan: null,
});

// Real WebSocket connection
const WS_URL = process.env.REACT_APP_WS_URL || 'ws://localhost:3001';

const createWebSocket = (onMessage: (data: LiveData) => void, onError: (error: Event) => void) => {
  try {
    const ws = new WebSocket(WS_URL);

    ws.onopen = () => {
      console.log('WebSocket connected to live data stream');
    };

    ws.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);
        onMessage(data);
      } catch (error) {
        console.error('Failed to parse WebSocket message:', error);
      }
    };

    ws.onerror = (error) => {
      console.error('WebSocket error:', error);
      onError(error);
    };

    ws.onclose = () => {
      console.log('WebSocket connection closed');
      // Attempt to reconnect after 5 seconds
      setTimeout(() => {
        console.log('Attempting to reconnect WebSocket...');
        createWebSocket(onMessage, onError);
      }, 5000);
    };

    return ws;
  } catch (error) {
    console.error('Failed to create WebSocket connection:', error);
    onError(error as Event);
    return null;
  }
};


export const LiveDataProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [liveData, setLiveData] = useState<LiveData | null>(null);
  const [sailPlan, setSailPlan] = useState<SailPlan>({
    headsail: SAIL_OPTIONS.HEADSAILS[0],
    spinnaker: SAIL_OPTIONS.SPINNAKERS[0],
    reef: SAIL_OPTIONS.REEFS[0],
  });
  const [loadingSailPlan, setLoadingSailPlan] = useState(true);
  const [errorSailPlan, setErrorSailPlan] = useState<string | null>(null);

  useEffect(() => {
    let ws: WebSocket | null = null;

    const handleMessage = (data: LiveData) => {
      setLiveData(data);
    };

    const handleError = (error: Event) => {
      console.error('WebSocket connection failed:', error);
      // Keep liveData as null when connection fails
      setLiveData(null);
    };

    // Attempt to connect to WebSocket
    ws = createWebSocket(handleMessage, handleError);

    // Cleanup function to close WebSocket connection
    return () => {
      if (ws && ws.readyState === WebSocket.OPEN) {
        ws.close();
      }
    };
  }, []);

  const loadSailPlan = useCallback(async () => {
    setLoadingSailPlan(true);
    setErrorSailPlan(null);
    try {
      const currentPlan = await fetchCurrentSailPlan();
      if (currentPlan) {
        setSailPlan(currentPlan);
      } else {
        // Use default sail plan if none is configured
        setSailPlan({
          headsail: SAIL_OPTIONS.HEADSAILS[0],
          spinnaker: SAIL_OPTIONS.SPINNAKERS[0],
          reef: SAIL_OPTIONS.REEFS[0],
        });
      }
    } catch (err) {
      setErrorSailPlan('Failed to load sail plan.');
      console.error(err);
      // Use default sail plan on error
      setSailPlan({
        headsail: SAIL_OPTIONS.HEADSAILS[0],
        spinnaker: SAIL_OPTIONS.SPINNAKERS[0],
        reef: SAIL_OPTIONS.REEFS[0],
      });
    } finally {
      setLoadingSailPlan(false);
    }
  }, []);

  useEffect(() => {
    loadSailPlan();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const updateSailPlan = useCallback(async (newPlan: SailPlan) => {
    setErrorSailPlan(null);
    try {
      await apiUpdateSailPlan(newPlan);
      setSailPlan(newPlan);
      // Optionally, emit this change via mockSocket if backend would confirm
      // mockSocket.emit('sailPlanUpdate', newPlan);
    } catch (err) {
      setErrorSailPlan('Failed to update sail plan.');
      console.error(err);
      throw err; // Re-throw to allow form to handle error
    }
  }, []);

  return (
    <LiveDataContext.Provider value={{ liveData, sailPlan, updateSailPlan, loadingSailPlan, errorSailPlan }}>
      {children}
    </LiveDataContext.Provider>
  );
};
