
import React, { createContext, useState, useEffect, ReactNode, useCallback } from 'react';
import { LiveData, SeaStateType, SailPlan } from '../types';
import { MOCK_SAIL_OPTIONS } from '../constants';
import { fetchCurrentSailPlan, updateSailPlan as apiUpdateSailPlan } from '../services/api'; // Mock API

interface LiveDataContextType {
  liveData: LiveData | null;
  sailPlan: SailPlan;
  updateSailPlan: (newPlan: SailPlan) => Promise<void>;
  loadingSailPlan: boolean;
  errorSailPlan: string | null;
}

export const LiveDataContext = createContext<LiveDataContextType>({
  liveData: null,
  sailPlan: {
    headsail: MOCK_SAIL_OPTIONS.HEADSAILS[0],
    spinnaker: MOCK_SAIL_OPTIONS.SPINNAKERS[0],
    reef: MOCK_SAIL_OPTIONS.REEFS[0],
  },
  updateSailPlan: async () => {},
  loadingSailPlan: true,
  errorSailPlan: null,
});

// Mock WebSocket connection
const mockSocket = {
  on: (event: string, callback: (data: LiveData) => void) => {
    if (event === 'liveUpdate') {
      setInterval(() => {
        // Simulate data fluctuations
        const bsp = 8 + Math.random() * 4 - 2; // 6-10 knots
        const tws = 12 + Math.random() * 6 - 3; // 9-15 knots
        const twa = (Math.random() > 0.5 ? 45 : 135) + Math.random() * 20 - 10; // Upwind or Downwind
        const vmg = bsp * Math.cos(twa * Math.PI / 180);
        const targetVMG = vmg * (1 + (Math.random() * 0.1 - 0.05)); // Target slightly around actual
        const percentPolar = (vmg / targetVMG) * 100;
        
        let tip = "On target";
        if (percentPolar < 90) tip = "Need more trim";
        if (twa < 40 && percentPolar < 95) tip = "Too low angle";
        if (twa > 150 && percentPolar < 95) tip = "Too high angle";

        const seaStates = Object.values(SeaStateType);
        const seaState = seaStates[Math.floor(Math.random() * seaStates.length)];

        callback({
          bsp: parseFloat(bsp.toFixed(1)),
          tws: parseFloat(tws.toFixed(1)),
          twa: parseFloat(twa.toFixed(1)),
          vmg: parseFloat(vmg.toFixed(1)),
          seaState,
          targetVMG: parseFloat(targetVMG.toFixed(1)),
          percentPolar: parseFloat(percentPolar.toFixed(1)),
          tip,
          heel: parseFloat((Math.random() * 15).toFixed(1)),
          pitch: parseFloat((Math.random() * 5 - 2.5).toFixed(1)),
          roll: parseFloat((Math.random() * 10 - 5).toFixed(1)),
          utcTime: new Date().toISOString(),
          lat: 34.0522 + (Math.random() - 0.5) * 0.01,
          lon: -118.2437 + (Math.random() - 0.5) * 0.01,
          heading: parseFloat((Math.random() * 360).toFixed(1)),
          sog: parseFloat((bsp + (Math.random()-0.5)).toFixed(1)), // SOG close to BSP
          cog: parseFloat((Math.random() * 360).toFixed(1)),
          depth: parseFloat((10 + Math.random() * 20).toFixed(1)), // 10-30m depth
          aws: parseFloat((tws * 1.1 + (Math.random()-0.5)).toFixed(1)), // AWS slightly higher
          awa: parseFloat((twa * 0.9 + (Math.random()-0.5)).toFixed(1)), // AWA slightly tighter
        });
      }, 2000); // Update every 2 seconds
    }
  },
  emit: (event: string, data: any) => {
    // console.log(`Mock socket emitting ${event}:`, data);
  }
};


export const LiveDataProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [liveData, setLiveData] = useState<LiveData | null>(null);
  const [sailPlan, setSailPlan] = useState<SailPlan>({
    headsail: MOCK_SAIL_OPTIONS.HEADSAILS[0],
    spinnaker: MOCK_SAIL_OPTIONS.SPINNAKERS[0],
    reef: MOCK_SAIL_OPTIONS.REEFS[0],
  });
  const [loadingSailPlan, setLoadingSailPlan] = useState(true);
  const [errorSailPlan, setErrorSailPlan] = useState<string | null>(null);

  useEffect(() => {
    mockSocket.on('liveUpdate', (data: LiveData) => {
      setLiveData(data);
    });
    // No cleanup function needed for this mock setInterval
    // In a real scenario, you'd close the WebSocket connection here.
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []); // Run once on mount

  const loadSailPlan = useCallback(async () => {
    setLoadingSailPlan(true);
    setErrorSailPlan(null);
    try {
      const currentPlan = await fetchCurrentSailPlan();
      setSailPlan(currentPlan);
    } catch (err) {
      setErrorSailPlan('Failed to load sail plan.');
      console.error(err);
    } finally {
      setLoadingSailPlan(false);
    }
  }, []);

  useEffect(() => {
    loadSailPlan();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const updateSailPlan = useCallback(async (newPlan: SailPlan) => {
    setErrorSailPlan(null);
    try {
      await apiUpdateSailPlan(newPlan);
      setSailPlan(newPlan);
      // Optionally, emit this change via mockSocket if backend would confirm
      // mockSocket.emit('sailPlanUpdate', newPlan);
    } catch (err) {
      setErrorSailPlan('Failed to update sail plan.');
      console.error(err);
      throw err; // Re-throw to allow form to handle error
    }
  }, []);

  return (
    <LiveDataContext.Provider value={{ liveData, sailPlan, updateSailPlan, loadingSailPlan, errorSailPlan }}>
      {children}
    </LiveDataContext.Provider>
  );
};
