
import React, { createContext, useState, useEffect, ReactNode, useCallback } from 'react';
import { PolarTable, PolarEntry } from '../types';
import { fetchPolarTables, updatePolarTable as apiUpdatePolarTable } from '../services/api'; // Mock API

interface PolarContextType {
  polarTables: PolarTable[];
  loading: boolean;
  error: string | null;
  getPolarTarget: (tws: number, twa: number, currentTable?: PolarTable) => PolarEntry | null;
  updatePolarTable: (tableId: string, updatedData: PolarEntry[]) => Promise<void>;
  addPolarTable: (newTable: Omit<PolarTable, 'id'>) => Promise<void>; // Assuming ID is generated by backend
}

// Dummy initial polar data
const initialMockPolarTables: PolarTable[] = [
  {
    name: 'Default Performance Cruiser',
    description: 'Generic polar for a 40ft cruiser/racer.',
    entries: [
      // TWS 6
      { tws: 6, twa: 30, targetSpeed: 3.5 }, { tws: 6, twa: 45, targetSpeed: 5.0 }, { tws: 6, twa: 60, targetSpeed: 5.5 },
      { tws: 6, twa: 90, targetSpeed: 5.8 }, { tws: 6, twa: 120, targetSpeed: 5.2 }, { tws: 6, twa: 150, targetSpeed: 4.0 },
      // TWS 10
      { tws: 10, twa: 30, targetSpeed: 4.8 }, { tws: 10, twa: 45, targetSpeed: 6.5 }, { tws: 10, twa: 60, targetSpeed: 7.2 },
      { tws: 10, twa: 90, targetSpeed: 7.8 }, { tws: 10, twa: 120, targetSpeed: 7.0 }, { tws: 10, twa: 150, targetSpeed: 5.5 },
      // TWS 16
      { tws: 16, twa: 30, targetSpeed: 5.5 }, { tws: 16, twa: 45, targetSpeed: 7.5 }, { tws: 16, twa: 60, targetSpeed: 8.5 },
      { tws: 16, twa: 90, targetSpeed: 9.5 }, { tws: 16, twa: 120, targetSpeed: 8.8 }, { tws: 16, twa: 150, targetSpeed: 7.0 },
       // TWS 20
      { tws: 20, twa: 30, targetSpeed: 6.0 }, { tws: 20, twa: 45, targetSpeed: 8.0 }, { tws: 20, twa: 60, targetSpeed: 9.0 },
      { tws: 20, twa: 90, targetSpeed: 10.5 },{ tws: 20, twa: 120, targetSpeed: 10.0 },{ tws: 20, twa: 150, targetSpeed: 8.0 },
    ]
  }
];


export const PolarContext = createContext<PolarContextType>({
  polarTables: [],
  loading: true,
  error: null,
  getPolarTarget: () => null,
  updatePolarTable: async () => {},
  addPolarTable: async () => {},
});

export const PolarProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [polarTables, setPolarTables] = useState<PolarTable[]>(initialMockPolarTables); // Start with mock data
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  const loadPolars = useCallback(async () => {
    setLoading(true);
    setError(null);
    try {
      // const tables = await fetchPolarTables(); // In a real app, fetch from API
      // setPolarTables(tables);
      // For now, using initialMockPolarTables already set.
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 500));
      setPolarTables(initialMockPolarTables); // Ensure it's set after mock delay
    } catch (err) {
      console.error("Failed to load polar tables:", err);
      setError("Failed to load polar tables.");
      setPolarTables(initialMockPolarTables); // Fallback to mock data on error
    } finally {
      setLoading(false);
    }
  }, []);
  
  useEffect(() => {
    loadPolars();
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Basic interpolation logic (can be made more sophisticated)
  const getPolarTarget = useCallback((tws: number, twa: number, currentTable?: PolarTable): PolarEntry | null => {
    const tableToUse = currentTable || (polarTables.length > 0 ? polarTables[0] : null);
    if (!tableToUse) return null;

    // Find closest TWS entries
    const uniqueTWS = Array.from(new Set(tableToUse.entries.map(e => e.tws))).sort((a, b) => a - b);
    let lowerTWS = uniqueTWS.filter(w => w <= tws).pop() || uniqueTWS[0];
    let upperTWS = uniqueTWS.filter(w => w >= tws).shift() || uniqueTWS[uniqueTWS.length - 1];
    if (lowerTWS === undefined) lowerTWS = upperTWS; // Handle edge case where tws is below min
    if (upperTWS === undefined) upperTWS = lowerTWS; // Handle edge case where tws is above max

    const getSpeedAtTWA = (windSpeed: number, windAngle: number): number | null => {
        const entriesForTWS = tableToUse.entries.filter(e => e.tws === windSpeed).sort((a,b) => a.twa - b.twa);
        if(entriesForTWS.length === 0) return null;

        let lowerTWAEntry = entriesForTWS.filter(e => e.twa <= windAngle).pop();
        let upperTWAEntry = entriesForTWS.filter(e => e.twa >= windAngle).shift();

        if(!lowerTWAEntry && !upperTWAEntry) return null; // No data for this TWS
        if(!lowerTWAEntry) return upperTWAEntry!.targetSpeed; // Exact match or below min TWA
        if(!upperTWAEntry) return lowerTWAEntry!.targetSpeed; // Exact match or above max TWA
        if(lowerTWAEntry.twa === upperTWAEntry.twa) return lowerTWAEntry.targetSpeed; // Exact TWA match

        const twaRange = upperTWAEntry.twa - lowerTWAEntry.twa;
        if (twaRange === 0) return lowerTWAEntry.targetSpeed; // Avoid division by zero
        
        const factor = (windAngle - lowerTWAEntry.twa) / twaRange;
        return lowerTWAEntry.targetSpeed + factor * (upperTWAEntry.targetSpeed - lowerTWAEntry.targetSpeed);
    };
    
    const speedAtLowerTWS = getSpeedAtTWA(lowerTWS, twa);
    if (lowerTWS === upperTWS || speedAtLowerTWS === null) {
        return speedAtLowerTWS ? { tws, twa, targetSpeed: speedAtLowerTWS } : null;
    }

    const speedAtUpperTWS = getSpeedAtTWA(upperTWS, twa);
    if (speedAtUpperTWS === null) { // Should not happen if speedAtLowerTWS is valid and TWS range exists
        return speedAtLowerTWS ? { tws, twa, targetSpeed: speedAtLowerTWS } : null;
    }

    const twsRange = upperTWS - lowerTWS;
    if (twsRange === 0) return { tws, twa, targetSpeed: speedAtLowerTWS }; // Should be caught by lowerTWS === upperTWS

    const factor = (tws - lowerTWS) / twsRange;
    const interpolatedSpeed = speedAtLowerTWS + factor * (speedAtUpperTWS - speedAtLowerTWS);

    return { tws, twa, targetSpeed: interpolatedSpeed };
  }, [polarTables]);


  const updatePolarTable = async (tableName: string, updatedEntries: PolarEntry[]) => {
    setLoading(true);
    try {
      // In a real app, this would be an API call:
      // await apiUpdatePolarTable(tableName, updatedEntries);
      setPolarTables(prevTables => 
        prevTables.map(table => 
          table.name === tableName ? { ...table, entries: updatedEntries } : table
        )
      );
      await new Promise(resolve => setTimeout(resolve, 300)); // Simulate API latency
    } catch (err) {
      console.error("Failed to update polar table:", err);
      setError("Failed to update polar table.");
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const addPolarTable = async (newTableData: Omit<PolarTable, 'name'> & {name:string}) => {
    setLoading(true);
    try {
      // const createdTable = await apiAddPolarTable(newTableData); // Real API call
      // In mock, just add to state with a generated ID (if needed) or use name as ID
      const createdTable = { ...newTableData }; // Assuming name is unique for now
      setPolarTables(prevTables => [...prevTables, createdTable]);
      await new Promise(resolve => setTimeout(resolve, 300)); // Simulate API latency
    } catch (err) {
      console.error("Failed to add polar table:", err);
      setError("Failed to add polar table.");
      throw err;
    } finally {
      setLoading(false);
    }
  };


  return (
    <PolarContext.Provider value={{ polarTables, loading, error, getPolarTarget, updatePolarTable, addPolarTable }}>
      {children}
    </PolarContext.Provider>
  );
};
