
import React, { createContext, useState, useEffect, ReactNode, useCallback } from 'react';
import { PolarTable, PolarEntry } from '../types';
import { fetchPolarTables, updatePolarTable as apiUpdatePolarTable, addPolarTable as apiAddPolarTable } from '../services/api';

interface PolarContextType {
  polarTables: PolarTable[];
  loading: boolean;
  error: string | null;
  getPolarTarget: (tws: number, twa: number, currentTable?: PolarTable) => PolarEntry | null;
  updatePolarTable: (tableId: string, updatedData: PolarEntry[]) => Promise<void>;
  addPolarTable: (newTable: Omit<PolarTable, 'id'>) => Promise<void>; // Assuming ID is generated by backend
}

// Initial empty polar data - will be loaded from real data source
const initialPolarTables: PolarTable[] = [];


export const PolarContext = createContext<PolarContextType>({
  polarTables: [],
  loading: true,
  error: null,
  getPolarTarget: () => null,
  updatePolarTable: async () => {},
  addPolarTable: async () => {},
});

export const PolarProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [polarTables, setPolarTables] = useState<PolarTable[]>(initialPolarTables);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  const loadPolars = useCallback(async () => {
    setLoading(true);
    setError(null);
    try {
      const tables = await fetchPolarTables();
      setPolarTables(tables);
    } catch (err) {
      console.error("Failed to load polar tables:", err);
      setError("Failed to load polar tables.");
      setPolarTables([]); // Set to empty array on error
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    loadPolars();
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Basic interpolation logic (can be made more sophisticated)
  const getPolarTarget = useCallback((tws: number, twa: number, currentTable?: PolarTable): PolarEntry | null => {
    const tableToUse = currentTable || (polarTables.length > 0 ? polarTables[0] : null);
    if (!tableToUse) return null;

    // Find closest TWS entries
    const uniqueTWS = Array.from(new Set(tableToUse.entries.map(e => e.tws))).sort((a, b) => a - b);
    let lowerTWS = uniqueTWS.filter(w => w <= tws).pop() || uniqueTWS[0];
    let upperTWS = uniqueTWS.filter(w => w >= tws).shift() || uniqueTWS[uniqueTWS.length - 1];
    if (lowerTWS === undefined) lowerTWS = upperTWS; // Handle edge case where tws is below min
    if (upperTWS === undefined) upperTWS = lowerTWS; // Handle edge case where tws is above max

    const getSpeedAtTWA = (windSpeed: number, windAngle: number): number | null => {
        const entriesForTWS = tableToUse.entries.filter(e => e.tws === windSpeed).sort((a,b) => a.twa - b.twa);
        if(entriesForTWS.length === 0) return null;

        let lowerTWAEntry = entriesForTWS.filter(e => e.twa <= windAngle).pop();
        let upperTWAEntry = entriesForTWS.filter(e => e.twa >= windAngle).shift();

        if(!lowerTWAEntry && !upperTWAEntry) return null; // No data for this TWS
        if(!lowerTWAEntry) return upperTWAEntry!.targetSpeed; // Exact match or below min TWA
        if(!upperTWAEntry) return lowerTWAEntry!.targetSpeed; // Exact match or above max TWA
        if(lowerTWAEntry.twa === upperTWAEntry.twa) return lowerTWAEntry.targetSpeed; // Exact TWA match

        const twaRange = upperTWAEntry.twa - lowerTWAEntry.twa;
        if (twaRange === 0) return lowerTWAEntry.targetSpeed; // Avoid division by zero

        const factor = (windAngle - lowerTWAEntry.twa) / twaRange;
        return lowerTWAEntry.targetSpeed + factor * (upperTWAEntry.targetSpeed - lowerTWAEntry.targetSpeed);
    };

    const speedAtLowerTWS = getSpeedAtTWA(lowerTWS, twa);
    if (lowerTWS === upperTWS || speedAtLowerTWS === null) {
        return speedAtLowerTWS ? { tws, twa, targetSpeed: speedAtLowerTWS } : null;
    }

    const speedAtUpperTWS = getSpeedAtTWA(upperTWS, twa);
    if (speedAtUpperTWS === null) { // Should not happen if speedAtLowerTWS is valid and TWS range exists
        return speedAtLowerTWS ? { tws, twa, targetSpeed: speedAtLowerTWS } : null;
    }

    const twsRange = upperTWS - lowerTWS;
    if (twsRange === 0) return { tws, twa, targetSpeed: speedAtLowerTWS }; // Should be caught by lowerTWS === upperTWS

    const factor = (tws - lowerTWS) / twsRange;
    const interpolatedSpeed = speedAtLowerTWS + factor * (speedAtUpperTWS - speedAtLowerTWS);

    return { tws, twa, targetSpeed: interpolatedSpeed };
  }, [polarTables]);


  const updatePolarTable = async (tableName: string, updatedEntries: PolarEntry[]) => {
    setLoading(true);
    try {
      const updatedTable = await apiUpdatePolarTable(tableName, updatedEntries);
      setPolarTables(prevTables =>
        prevTables.map(table =>
          table.name === tableName ? updatedTable : table
        )
      );
    } catch (err) {
      console.error("Failed to update polar table:", err);
      setError("Failed to update polar table.");
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const addPolarTable = async (newTableData: Omit<PolarTable, 'name'> & {name:string}) => {
    setLoading(true);
    try {
      const createdTable = await apiAddPolarTable(newTableData);
      setPolarTables(prevTables => [...prevTables, createdTable]);
    } catch (err) {
      console.error("Failed to add polar table:", err);
      setError("Failed to add polar table.");
      throw err;
    } finally {
      setLoading(false);
    }
  };


  return (
    <PolarContext.Provider value={{ polarTables, loading, error, getPolarTarget, updatePolarTable, addPolarTable }}>
      {children}
    </PolarContext.Provider>
  );
};
