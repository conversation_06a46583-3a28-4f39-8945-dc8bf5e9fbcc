
// This file is largely conceptual for a frontend-only mock.
// The actual "socket" logic is simulated within LiveDataContext.tsx for simplicity in this context.
// In a real application, you would initialize and manage the Socket.IO client here.

// import io from 'socket.io-client'; // This would be used in a real app with npm

interface MockSocket {
  on: (event: string, callback: (data: any) => void) => void;
  emit: (event: string, data: any) => void;
  connect: () => void;
  disconnect: () => void;
}

let connected = false;
const listeners: { [event: string]: Array<(data: any) => void> } = {};

const socket: MockSocket = {
  on: (event, callback) => {
    if (!listeners[event]) {
      listeners[event] = [];
    }
    listeners[event].push(callback);
    // console.log(`MockSocket: Listener registered for event '${event}'`);
  },
  emit: (event, data) => {
    // console.log(`MockSocket: Emitting event '${event}' with data:`, data);
    // This would send data to a mock server or log it.
    // For example, if emitting 'setSailPlan', the LiveDataContext simulation could pick this up.
  },
  connect: () => {
    if (!connected) {
      // console.log('MockSocket: Connecting...');
      connected = true;
      // Simulate connection success
      setTimeout(() => {
        // console.log('MockSocket: Connected.');
        if (listeners['connect']) {
          listeners['connect'].forEach(cb => cb(null));
        }
      }, 500);
    }
  },
  disconnect: () => {
    if (connected) {
      // console.log('MockSocket: Disconnecting...');
      connected = false;
      // Simulate disconnection
      if (listeners['disconnect']) {
        listeners['disconnect'].forEach(cb => cb(null));
      }
    }
  },
};

// Example of how LiveDataContext's mock might use this structure (though it's self-contained now):
// If LiveDataContext used this shared mock:
// It would call socket.on('liveUpdate', handler)
// The setInterval in LiveDataContext would instead call:
// if (listeners['liveUpdate']) {
//   listeners['liveUpdate'].forEach(cb => cb(simulatedLiveData));
// }

export default socket;
