
import { PolarTable, RaceEvent, CrewMember, CrewRSVP, SailPlan, PerformanceDataPoint, TackJibeInfo, PolarEntry, SeaStateType } from '../types';
import { SAIL_OPTIONS, DEFAULT_CREW_MEMBERS } from '../constants';

// Real API base URL - should be configured via environment variables
const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:3001/api';

// Real API call helper
const apiCall = async <T,>(endpoint: string, options?: RequestInit): Promise<T> => {
  try {
    const response = await fetch(`${API_BASE_URL}${endpoint}`, {
      headers: {
        'Content-Type': 'application/json',
        ...options?.headers,
      },
      ...options,
    });

    if (!response.ok) {
      throw new Error(`API call failed: ${response.status} ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error(`API call to ${endpoint} failed:`, error);
    throw error;
  }
};

// --- Data Storage (will be replaced by real backend) ---
let currentSailPlan: SailPlan | null = null;
let polarTables: PolarTable[] = [];
let raceEvents: RaceEvent[] = [];
let crewRSVPs: CrewRSVP[] = [];

// --- SailPlan API ---
export const fetchCurrentSailPlan = async (): Promise<SailPlan | null> => {
  try {
    return await apiCall<SailPlan>('/sailplan');
  } catch (error) {
    // Return null if no sail plan is configured yet
    return null;
  }
};

export const updateSailPlan = async (newPlan: SailPlan): Promise<SailPlan> => {
  try {
    return await apiCall<SailPlan>('/sailplan', {
      method: 'PUT',
      body: JSON.stringify(newPlan),
    });
  } catch (error) {
    // Fallback: store locally if API is not available
    currentSailPlan = { ...newPlan };
    return currentSailPlan;
  }
};

// --- Polar API ---
export const fetchPolarTables = async (): Promise<PolarTable[]> => {
  try {
    return await apiCall<PolarTable[]>('/polars');
  } catch (error) {
    // Return empty array if no polars are configured yet
    return [];
  }
};

export const updatePolarTable = async (tableName: string, updatedData: PolarEntry[]): Promise<PolarTable> => {
  try {
    const updatedTable = await apiCall<PolarTable>(`/polars/${encodeURIComponent(tableName)}`, {
      method: 'PUT',
      body: JSON.stringify({ entries: updatedData }),
    });
    return updatedTable;
  } catch (error) {
    // Fallback: store locally if API is not available
    const tableIndex = polarTables.findIndex(t => t.name === tableName);
    if (tableIndex === -1) throw new Error("Polar table not found");
    polarTables[tableIndex].entries = updatedData;
    return polarTables[tableIndex];
  }
};

export const addPolarTable = async (newTableData: Omit<PolarTable, 'name'> & {name: string}): Promise<PolarTable> => {
  try {
    return await apiCall<PolarTable>('/polars', {
      method: 'POST',
      body: JSON.stringify(newTableData),
    });
  } catch (error) {
    // Fallback: store locally if API is not available
    const newTable: PolarTable = { ...newTableData };
    polarTables.push(newTable);
    return newTable;
  }
};


// --- Races API ---
export const fetchRaceEvents = async (): Promise<RaceEvent[]> => {
  try {
    return await apiCall<RaceEvent[]>('/races');
  } catch (error) {
    // Return empty array if no races are configured yet
    return [];
  }
};

export const createRaceEvent = async (raceData: Omit<RaceEvent, 'id'>): Promise<RaceEvent> => {
  try {
    return await apiCall<RaceEvent>('/races', {
      method: 'POST',
      body: JSON.stringify(raceData),
    });
  } catch (error) {
    // Fallback: store locally if API is not available
    const newRace: RaceEvent = { ...raceData, id: `race${Date.now()}` };
    raceEvents.push(newRace);
    return newRace;
  }
};

export const updateRaceEvent = async (raceId: string, raceData: Partial<RaceEvent>): Promise<RaceEvent> => {
  try {
    return await apiCall<RaceEvent>(`/races/${raceId}`, {
      method: 'PUT',
      body: JSON.stringify(raceData),
    });
  } catch (error) {
    // Fallback: store locally if API is not available
    const raceIndex = raceEvents.findIndex(r => r.id === raceId);
    if (raceIndex === -1) throw new Error('Race not found');
    raceEvents[raceIndex] = { ...raceEvents[raceIndex], ...raceData };
    return raceEvents[raceIndex];
  }
};

export const deleteRaceEvent = async (raceId: string): Promise<void> => {
  try {
    await apiCall(`/races/${raceId}`, {
      method: 'DELETE',
    });
  } catch (error) {
    // Fallback: remove locally if API is not available
    raceEvents = raceEvents.filter(r => r.id !== raceId);
  }
};

// --- Crew API ---
export const fetchCrewMembers = async (): Promise<CrewMember[]> => {
  try {
    return await apiCall<CrewMember[]>('/crew');
  } catch (error) {
    // Return empty array if no crew members are configured yet
    return [];
  }
};

export const fetchCrewRSVPs = async (raceId: string): Promise<CrewRSVP[]> => {
  try {
    return await apiCall<CrewRSVP[]>(`/races/${raceId}/rsvps`);
  } catch (error) {
    // Return empty array if no RSVPs exist yet
    return [];
  }
};

export const updateCrewRSVP = async (rsvp: CrewRSVP): Promise<CrewRSVP> => {
  try {
    return await apiCall<CrewRSVP>(`/races/${rsvp.raceId}/rsvps`, {
      method: 'PUT',
      body: JSON.stringify(rsvp),
    });
  } catch (error) {
    // Fallback: store locally if API is not available
    const rsvpIndex = crewRSVPs.findIndex(r => r.raceId === rsvp.raceId && r.crewMemberId === rsvp.crewMemberId);
    if (rsvpIndex !== -1) {
      crewRSVPs[rsvpIndex] = rsvp;
    } else {
      crewRSVPs.push(rsvp);
    }
    return rsvp;
  }
};

// --- Report API ---
export const fetchPerformanceReportSummary = async (raceId: string): Promise<any> => {
  try {
    return await apiCall<any>(`/races/${raceId}/summary`);
  } catch (error) {
    // Return null if no performance data exists for this race
    return null;
  }
};

export const fetchPerformanceDataForRace = async (raceId: string): Promise<PerformanceDataPoint[]> => {
  try {
    return await apiCall<PerformanceDataPoint[]>(`/races/${raceId}/performance`);
  } catch (error) {
    // Return empty array if no performance data exists for this race
    return [];
  }
};

export const fetchTackJibeAnalysis = async (raceId: string): Promise<TackJibeInfo[]> => {
  try {
    return await apiCall<TackJibeInfo[]>(`/races/${raceId}/maneuvers`);
  } catch (error) {
    // Return empty array if no maneuver data exists for this race
    return [];
  }
};