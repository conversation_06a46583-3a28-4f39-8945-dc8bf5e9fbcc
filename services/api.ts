
import { PolarTable, RaceEvent, CrewMember, CrewRSVP, SailPlan, PerformanceDataPoint, TackJibeInfo, PolarEntry, SeaStateType } from '../types';
import { MOCK_SAIL_OPTIONS, MOCK_CREW_MEMBERS } from '../constants';

// Simulate a delay for API calls
const apiDelay = <T,>(data: T, delay: number = 500): Promise<T> => 
  new Promise(resolve => setTimeout(() => resolve(data), delay));

// --- Mock Data Store ---
let mockSailPlan: SailPlan = {
  headsail: MOCK_SAIL_OPTIONS.HEADSAILS[0],
  spinnaker: MOCK_SAIL_OPTIONS.SPINNAKERS[0],
  reef: MOCK_SAIL_OPTIONS.REEFS[0],
};

let mockPolarTables: PolarTable[] = [
  {
    name: 'Default Performance Cruiser',
    description: 'Generic polar for a 40ft cruiser/racer.',
    entries: [
      { tws: 6, twa: 45, targetSpeed: 5.0 }, { tws: 6, twa: 90, targetSpeed: 5.8 }, { tws: 6, twa: 135, targetSpeed: 5.0 },
      { tws: 10, twa: 45, targetSpeed: 6.5 }, { tws: 10, twa: 90, targetSpeed: 7.8 }, { tws: 10, twa: 135, targetSpeed: 7.0 },
      { tws: 16, twa: 45, targetSpeed: 7.5 }, { tws: 16, twa: 90, targetSpeed: 9.5 }, { tws: 16, twa: 135, targetSpeed: 8.8 },
    ]
  }
];

let mockRaceEvents: RaceEvent[] = [
  { id: 'race1', name: 'Spring Regatta - Race 1', startTime: new Date(Date.now() + 24 * 3600 * 1000), route: [{lat: 34.00, lon: -118.30, name: "Start"}, {lat: 34.05, lon: -118.35, name: "Mark A"}, {lat: 34.00, lon: -118.40, name: "Mark B"}] },
  { id: 'race2', name: 'Summer Challenge', startTime: new Date(Date.now() + 7 * 24 * 3600 * 1000), route: [{lat: 33.95, lon: -118.25, name: "Start/Finish"}] },
];

let mockCrewRSVPs: CrewRSVP[] = [
  { raceId: 'race1', crewMemberId: 'crew1', status: 'Attending' },
  { raceId: 'race1', crewMemberId: 'crew2', status: 'Maybe' },
];

// --- SailPlan API ---
export const fetchCurrentSailPlan = async (): Promise<SailPlan> => {
  return apiDelay(mockSailPlan);
};

export const updateSailPlan = async (newPlan: SailPlan): Promise<SailPlan> => {
  mockSailPlan = { ...newPlan };
  return apiDelay(mockSailPlan);
};

// --- Polar API ---
export const fetchPolarTables = async (): Promise<PolarTable[]> => {
  return apiDelay(mockPolarTables);
};

export const updatePolarTable = async (tableName: string, updatedData: PolarEntry[]): Promise<PolarTable> => {
  const tableIndex = mockPolarTables.findIndex(t => t.name === tableName);
  if (tableIndex === -1) throw new Error("Polar table not found");
  mockPolarTables[tableIndex].entries = updatedData;
  return apiDelay(mockPolarTables[tableIndex]);
};

export const addPolarTable = async (newTableData: Omit<PolarTable, 'name'> & {name: string}): Promise<PolarTable> => {
  const newTable: PolarTable = { ...newTableData };
  mockPolarTables.push(newTable);
  return apiDelay(newTable);
};


// --- Races API ---
export const fetchRaceEvents = async (): Promise<RaceEvent[]> => {
  return apiDelay(mockRaceEvents);
};

export const createRaceEvent = async (raceData: Omit<RaceEvent, 'id'>): Promise<RaceEvent> => {
  const newRace: RaceEvent = { ...raceData, id: `race${Date.now()}` };
  mockRaceEvents.push(newRace);
  return apiDelay(newRace);
};

export const updateRaceEvent = async (raceId: string, raceData: Partial<RaceEvent>): Promise<RaceEvent> => {
  const raceIndex = mockRaceEvents.findIndex(r => r.id === raceId);
  if (raceIndex === -1) throw new Error('Race not found');
  mockRaceEvents[raceIndex] = { ...mockRaceEvents[raceIndex], ...raceData };
  return apiDelay(mockRaceEvents[raceIndex]);
};

export const deleteRaceEvent = async (raceId: string): Promise<void> => {
  mockRaceEvents = mockRaceEvents.filter(r => r.id !== raceId);
  return apiDelay(undefined);
};

// --- Crew API ---
export const fetchCrewMembers = async (): Promise<CrewMember[]> => {
  return apiDelay(MOCK_CREW_MEMBERS);
};

export const fetchCrewRSVPs = async (raceId: string): Promise<CrewRSVP[]> => {
  return apiDelay(mockCrewRSVPs.filter(rsvp => rsvp.raceId === raceId));
};

export const updateCrewRSVP = async (rsvp: CrewRSVP): Promise<CrewRSVP> => {
  const rsvpIndex = mockCrewRSVPs.findIndex(r => r.raceId === rsvp.raceId && r.crewMemberId === rsvp.crewMemberId);
  if (rsvpIndex !== -1) {
    mockCrewRSVPs[rsvpIndex] = rsvp;
  } else {
    mockCrewRSVPs.push(rsvp);
  }
  return apiDelay(rsvp);
};

// --- Report API ---
export const fetchPerformanceReportSummary = async (raceId: string): Promise<any> => {
  // Mock summary data
  return apiDelay({
    raceId,
    avgBoatSpeed: 7.2,
    maxBoatSpeed: 10.5,
    avgPercentPolar: 92.3,
    timeAtOptimal: '65%',
    totalDistance: 15.2, // Nautical miles
    tacks: 8,
    jibes: 5,
  });
};

export const fetchPerformanceDataForRace = async (raceId: string): Promise<PerformanceDataPoint[]> => {
  // Mock time series data
  const data: PerformanceDataPoint[] = [];
  const startTime = Date.now() - 3600 * 1000 * 2; // 2 hours ago
  for (let i = 0; i < 120; i++) { // 120 points, 1 per minute
    const timestamp = startTime + i * 60 * 1000;
    const bsp = 6 + Math.random() * 4;
    const tws = 10 + Math.random() * 5;
    const twa = Math.random() > 0.5 ? (40 + Math.random() * 15) : (130 + Math.random() * 20) ;
    const targetSpeed = (tws * 0.7) * (1 - Math.abs(90-twa)/180); // very rough polar
    const percentPolar = (bsp / targetSpeed) * 100;
    data.push({
      timestamp,
      bsp: parseFloat(bsp.toFixed(1)),
      tws: parseFloat(tws.toFixed(1)),
      twa: parseFloat(twa.toFixed(1)),
      vmg: parseFloat((bsp * Math.cos(twa * Math.PI / 180)).toFixed(1)),
      targetVMG: parseFloat((targetSpeed * Math.cos(twa * Math.PI / 180)).toFixed(1)),
      percentPolar: parseFloat(percentPolar.toFixed(1)),
// Fix: Use SeaStateType enum members for type safety
      seaState: Math.random() > 0.7 ? SeaStateType.MODERATE : SeaStateType.CALM,
      tip: percentPolar > 95 ? 'On target' : 'Needs improvement',
      heel: parseFloat((5 + Math.random() * 10).toFixed(1)),
      pitch: parseFloat((Math.random()*2-1).toFixed(1)),
      roll: parseFloat((Math.random()*4-2).toFixed(1)),
      utcTime: new Date(timestamp).toISOString()
    });
  }
  return apiDelay(data);
};

export const fetchTackJibeAnalysis = async (raceId: string): Promise<TackJibeInfo[]> => {
  const analysis: TackJibeInfo[] = [];
  for (let i = 0; i < 5 + Math.floor(Math.random()*5) ; i++) {
    analysis.push({
      id: `maneuver${i}`,
      type: Math.random() > 0.5 ? 'Tack' : 'Jibe',
      startTime: Date.now() - (Math.random() * 3600 * 1000),
      endTime: Date.now() - (Math.random() * 3600 * 1000) + (15 + Math.random() * 10) * 1000,
      durationSeconds: parseFloat((15 + Math.random() * 10).toFixed(1)),
      distanceLostMeters: parseFloat((5 + Math.random() * 20).toFixed(1)),
      entrySpeed: parseFloat((6 + Math.random() * 2).toFixed(1)),
      exitSpeed: parseFloat((5 + Math.random() * 2).toFixed(1)),
      avgSpeedDuringManeuver: parseFloat((4 + Math.random() * 1.5).toFixed(1)),
    });
  }
  return apiDelay(analysis.sort((a,b) => a.startTime - b.startTime));
};