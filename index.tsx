
import React from 'react';
import ReactDOM from 'react-dom/client';
import App from './App';
import { LiveDataProvider } from './contexts/LiveDataContext';
import { PolarProvider } from './contexts/PolarContext';
import { HashRouter } from 'react-router-dom'; // Import HashRouter directly

const rootElement = document.getElementById('root');
if (!rootElement) {
  throw new Error("Could not find root element to mount to");
}

const root = ReactDOM.createRoot(rootElement);
root.render(
  <React.StrictMode>
    <HashRouter>
      <PolarProvider>
        <LiveDataProvider>
          <App />
        </LiveDataProvider>
      </PolarProvider>
    </HashRouter>
  </React.StrictMode>
);