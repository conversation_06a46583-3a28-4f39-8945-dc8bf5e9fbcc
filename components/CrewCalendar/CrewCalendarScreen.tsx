import React, { useState, useEffect, useCallback } from 'react';
import { RaceEvent, CrewMember, CrewRSVP } from '../../types';
import { fetchRaceEvents, fetchCrewMembers, fetchCrewRSVPs, updateCrewRSVP } from '../../services/api';
import Card from '../ui/Card';
import Button from '../ui/Button';
import Modal from '../ui/Modal';
import Select from '../ui/Select';
import { MOCK_CREW_MEMBERS } from '../../constants';
import Badge from '../ui/Badge';

const getRaceUrgency = (startTime: Date): { label?: string, color?: 'amber' | 'red' } => {
  const now = new Date();
  const diffHours = (startTime.getTime() - now.getTime()) / (1000 * 60 * 60);
  if (diffHours < 0) return {}; 
  if (diffHours <= 24) return { label: 'Today!', color: 'red' };
  if (diffHours <= 72) return { label: 'Soon', color: 'amber' };
  return {};
};

const CrewCalendarScreen: React.FC = () => {
  const [races, setRaces] = useState<RaceEvent[]>([]);
  const [crewMembers, setCrewMembers] = useState<CrewMember[]>(MOCK_CREW_MEMBERS); 
  const [rsvps, setRsvps] = useState<Record<string, CrewRSVP[]>>({});
  
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const [selectedRaceForRSVP, setSelectedRaceForRSVP] = useState<RaceEvent | null>(null);
  const [isRsvpModalOpen, setIsRsvpModalOpen] = useState(false);
  const [isSubmittingRsvp, setIsSubmittingRsvp] = useState(false);


  const loadData = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    try {
      const raceData = await fetchRaceEvents();
      setRaces(raceData.sort((a,b) => new Date(a.startTime).getTime() - new Date(b.startTime).getTime())); 

      const rsvpMap: Record<string, CrewRSVP[]> = {};
      if (raceData.length > 0) {
        const rsvpPromises = raceData.map(race => fetchCrewRSVPs(race.id));
        const rsvpResults = await Promise.all(rsvpPromises);
        raceData.forEach((race, index) => {
          rsvpMap[race.id] = rsvpResults[index];
        });
      }
      setRsvps(rsvpMap);

    } catch (err) {
      console.error("Failed to load crew calendar data:", err);
      setError("Could not load race or crew data. Please try again later.");
    } finally {
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    loadData();
  }, [loadData]);

  const handleOpenRsvpModal = (race: RaceEvent) => {
    setSelectedRaceForRSVP(race);
    setIsRsvpModalOpen(true);
  };

  const handleCloseRsvpModal = () => {
    setIsRsvpModalOpen(false);
    setSelectedRaceForRSVP(null);
  };

  const handleRsvpChange = async (raceId: string, crewMemberId: string, status: 'Attending' | 'Not Attending' | 'Maybe') => {
    setIsSubmittingRsvp(true);
    try {
      const updatedRsvp = await updateCrewRSVP({ raceId, crewMemberId, status });
      setRsvps(prevRsvps => {
        const raceRsvps = prevRsvps[raceId] ? [...prevRsvps[raceId]] : [];
        const existingRsvpIndex = raceRsvps.findIndex(r => r.crewMemberId === crewMemberId);
        if (existingRsvpIndex > -1) {
          raceRsvps[existingRsvpIndex] = updatedRsvp;
        } else {
          raceRsvps.push(updatedRsvp);
        }
        return { ...prevRsvps, [raceId]: raceRsvps };
      });
    } catch (err) {
      console.error("Failed to update RSVP:", err);
      alert("Error updating RSVP. Please try again."); 
    } finally {
      setIsSubmittingRsvp(false);
    }
  };
  
  const getRsvpStats = (raceId: string) => {
    const raceRsvps = rsvps[raceId] || [];
    const attending = raceRsvps.filter(r => r.status === 'Attending').length;
    const maybe = raceRsvps.filter(r => r.status === 'Maybe').length;
    return { attending, maybe, total: raceRsvps.length };
  };


  if (isLoading) return (
    <div className="flex flex-col items-center justify-center py-20 perspective-racing-text-secondary">
      <svg className="animate-spin h-10 w-10 perspective-racing-accent-sky mb-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
      </svg>
      Loading crew calendar...
    </div>
  );
  if (error) return <p className="text-red-400 bg-red-700/30 p-4 rounded-md text-center">{error}</p>;

  return (
    <div className="space-y-8">
      <div className="flex flex-col sm:flex-row justify-between items-center gap-4">
        <h1 className="text-3xl font-bold perspective-racing-text-primary">Crew Calendar</h1>
      </div>

      {races.length === 0 ? (
        <Card>
          <p className="perspective-racing-text-secondary text-center py-10">No upcoming races scheduled. Time to plan the next adventure!</p>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {races.map(race => {
            const urgency = getRaceUrgency(new Date(race.startTime));
            const stats = getRsvpStats(race.id);
            return (
            <Card key={race.id} title={race.name} className="flex flex-col justify-between hover:shadow-sky-500/30 transition-shadow duration-200">
              <div className="flex-grow">
                <div className="flex justify-between items-start mb-2">
                    <p className="text-sm perspective-racing-text-primary">
                    {new Date(race.startTime).toLocaleDateString([], { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' })}
                    <br />
                    at {new Date(race.startTime).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})}
                    </p>
                    {urgency.label && <Badge color={urgency.color} size="sm">{urgency.label}</Badge>}
                </div>
                
                {race.route && <p className="text-xs perspective-racing-text-secondary mt-1 mb-3">Marks: {race.route.length}, Notes: {race.notes || 'None'}</p>}
                
                <div className="mt-3 border-t perspective-racing-border pt-3">
                  <h4 className="text-xs font-semibold perspective-racing-text-secondary uppercase mb-1">Attendance:</h4>
                  <div className="flex space-x-4 text-sm">
                    <span className="text-green-400">Attending: {stats.attending}</span>
                    <span className="text-amber-400">Maybe: {stats.maybe}</span>
                  </div>
                </div>
              </div>
              <div className="mt-4 pt-4 border-t perspective-racing-border">
                <Button onClick={() => handleOpenRsvpModal(race)} size="md" className="w-full" variant="secondary">
                  Manage RSVPs
                </Button>
              </div>
            </Card>
          );
        })}
        </div>
      )}

      {selectedRaceForRSVP && (
        <Modal
          isOpen={isRsvpModalOpen}
          onClose={handleCloseRsvpModal}
          title={`RSVP for: ${selectedRaceForRSVP.name}`}
          size="md"
          footer={
            <Button onClick={handleCloseRsvpModal} variant="primary" size="md">Done</Button>
          }
        >
          <div className="space-y-3 max-h-[60vh] overflow-y-auto pr-1 -mr-2 custom-scrollbar">
            {crewMembers.map(crew => {
              const currentRsvp = rsvps[selectedRaceForRSVP.id]?.find(r => r.crewMemberId === crew.id);
              const status = currentRsvp?.status || 'Not Attending'; 
              return (
                <div key={crew.id} className="flex flex-col sm:flex-row items-start sm:items-center justify-between p-3.5 bg-slate-700 rounded-lg shadow-sm gap-3"> {/* Using slate-700 for item bg to differentiate from modal's card-bg */}
                  <div>
                    <p className="font-semibold perspective-racing-text-primary">{crew.name}</p>
                    {crew.role && <p className="text-xs perspective-racing-text-secondary">{crew.role}</p>}
                  </div>
                  <Select
                    id={`rsvp-${crew.id}`}
                    value={status}
                    aria-label={`RSVP status for ${crew.name}`}
                    onChange={(e) => handleRsvpChange(selectedRaceForRSVP.id, crew.id, e.target.value as any)}
                    options={[
                      { value: 'Attending', label: '✅ Attending' },
                      { value: 'Maybe', label: '❓ Maybe' },
                      { value: 'Not Attending', label: '❌ Not Attending' },
                    ]}
                    className="w-full sm:w-48 h-9 text-sm" 
                    disabled={isSubmittingRsvp}
                  />
                </div>
              );
            })}
          </div>
           <style>{`
            .custom-scrollbar::-webkit-scrollbar { width: 6px; }
            .custom-scrollbar::-webkit-scrollbar-track { background: #334155; border-radius:3px; } /* perspective-racing-border */
            .custom-scrollbar::-webkit-scrollbar-thumb { background: #64748B; border-radius:3px; } /* slate-500 */
            .custom-scrollbar::-webkit-scrollbar-thumb:hover { background: #94A3B8; } /* perspective-racing-text-secondary */
           `}</style>
        </Modal>
      )}
    </div>
  );
};

export default CrewCalendarScreen;