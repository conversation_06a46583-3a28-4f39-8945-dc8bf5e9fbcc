import React, { useContext, useState, useEffect } from 'react';
import { PolarContext } from '../../contexts/PolarContext';
import { PolarTable, PolarEntry } from '../../types';
import Button from '../ui/Button';
import Input from '../ui/Input';
import Select from '../ui/Select';

const PolarTableEditor: React.FC = () => {
  const { polarTables, loading, error, updatePolarTable, addPolarTable } = useContext(PolarContext);
  const [selectedTableName, setSelectedTableName] = useState<string>('');
  const [editableEntries, setEditableEntries] = useState<PolarEntry[]>([]);
  const [isEditingNewTable, setIsEditingNewTable] = useState(false);
  const [newTableName, setNewTableName] = useState('');
  const [newTableDescription, setNewTableDescription] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [message, setMessage] = useState<{type: 'success' | 'error', text: string} | null>(null);

  useEffect(() => {
    if (polarTables.length > 0 && !selectedTableName && !isEditingNewTable) {
      setSelectedTableName(polarTables[0].name);
    }
  }, [polarTables, selectedTableName, isEditingNewTable]);

  useEffect(() => {
    setMessage(null); 
    if (selectedTableName && !isEditingNewTable) {
      const currentTable = polarTables.find(t => t.name === selectedTableName);
      if (currentTable) {
        setEditableEntries(JSON.parse(JSON.stringify(currentTable.entries.sort((a,b) => a.tws - b.tws || a.twa - b.twa))));
      }
    } else if (isEditingNewTable) {
        setEditableEntries([{ tws: 6, twa: 45, targetSpeed: 5.0 }]); 
    } else {
        setEditableEntries([]);
    }
  }, [selectedTableName, polarTables, isEditingNewTable]);

  const handleEntryChange = (index: number, field: keyof PolarEntry, value: string) => {
    const newEntries = [...editableEntries];
    if (field === 'tws' || field === 'twa' || field === 'targetSpeed') {
        const numValue = parseFloat(value);
        newEntries[index] = { ...newEntries[index], [field]: isNaN(numValue) ? 0 : numValue };
    } else {
        (newEntries[index] as any)[field] = value;
    }
    setEditableEntries(newEntries);
    setMessage(null);
  };

  const addEntryRow = () => {
    const lastEntry = editableEntries[editableEntries.length - 1] || { tws: 0, twa: 0, targetSpeed: 0 };
    setEditableEntries([...editableEntries, { ...lastEntry, targetSpeed: 0 }]); 
  };

  const removeEntryRow = (index: number) => {
    setEditableEntries(editableEntries.filter((_, i) => i !== index));
  };

  const handleSubmit = async () => {
    setIsSubmitting(true);
    setMessage(null);
    try {
      const processedEntries = editableEntries.filter(e => e.tws > 0 && e.twa >= 0 && e.twa <= 180 && e.targetSpeed >= 0);
      if(processedEntries.length !== editableEntries.length){
        setMessage({type: 'error', text: "Some rows had invalid data (e.g. TWS <=0) and were ignored. Please review."});
      }
      if(processedEntries.length === 0){
        setMessage({type: 'error', text: "Cannot save an empty or invalid polar table."});
        setIsSubmitting(false);
        return;
      }

      if (isEditingNewTable) {
        if (!newTableName.trim()) {
          setMessage({type: 'error', text: "New table name cannot be empty."});
          setIsSubmitting(false);
          return;
        }
        await addPolarTable({ name: newTableName.trim(), description: newTableDescription.trim(), entries: processedEntries });
        setSelectedTableName(newTableName.trim());
        setIsEditingNewTable(false);
        setNewTableName('');
        setNewTableDescription('');
        setMessage({type: 'success', text: "New polar table added successfully!"});
      } else if (selectedTableName) {
        await updatePolarTable(selectedTableName, processedEntries);
        setMessage({type: 'success', text: "Polar table updated successfully!"});
      }
    } catch (err) {
      setMessage({type: 'error', text: `Error: Failed to save polar table. ${(err as Error).message}`});
    } finally {
      setIsSubmitting(false);
      setTimeout(() => setMessage(null), 5000);
    }
  };
  
  const startNewTableMode = () => {
    setIsEditingNewTable(true);
    setSelectedTableName(''); 
    setNewTableName('');
    setNewTableDescription('');
    setEditableEntries([{ tws: 6, twa: 45, targetSpeed: 5.0 }]);
  };

  const cancelNewTableMode = () => {
    setIsEditingNewTable(false);
    setNewTableName('');
    setNewTableDescription('');
    if (polarTables.length > 0) {
        setSelectedTableName(polarTables[0].name);
    }
  };

  const currentTableDescription = polarTables.find(t => t.name === selectedTableName)?.description;

  if (loading && polarTables.length === 0) return <div className="flex items-center justify-center py-10 perspective-racing-text-secondary"><svg className="animate-spin -ml-1 mr-3 h-5 w-5 perspective-racing-accent-sky" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><circle cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" className="opacity-25"></circle><path d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" className="opacity-75" fill="currentColor"></path></svg>Loading polar tables...</div>;
  if (error && !loading) return <p className="text-red-400 bg-red-700/30 p-3 rounded-md">{error}</p>;

  const tableOptions = polarTables.map(table => ({ value: table.name, label: table.name }));

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row md:items-end md:justify-between gap-4 p-4 perspective-racing-input-bg rounded-lg shadow">
        {isEditingNewTable ? (
          <div className="flex-grow space-y-4">
            <Input 
                label="New Polar Table Name:"
                id="newTableName"
                value={newTableName}
                onChange={(e) => setNewTableName(e.target.value)}
                placeholder="e.g., J/105 Light Air"
                containerClassName="max-w-md"
                required
            />
            <Input 
                label="Description (Optional):"
                id="newTableDescription"
                value={newTableDescription}
                onChange={(e) => setNewTableDescription(e.target.value)}
                placeholder="e.g., For winds up to 12 knots"
                containerClassName="max-w-md"
            />
          </div>
        ) : (
          <div className="flex-grow">
            <Select
              id="polarTableSelect"
              label="Select Polar Table:"
              options={tableOptions}
              value={selectedTableName}
              onChange={(e) => { setSelectedTableName(e.target.value); setIsEditingNewTable(false); }}
              disabled={isEditingNewTable || tableOptions.length === 0}
              placeholder={tableOptions.length === 0 ? "No tables available" : "Choose a table"}
              containerClassName="max-w-md"
            />
            {selectedTableName && currentTableDescription && (
              <p className="text-xs perspective-racing-text-secondary mt-1.5 italic">{currentTableDescription}</p>
            )}
          </div>
        )}
        <div className="flex-shrink-0 flex flex-col sm:flex-row gap-2 pt-2 md:pt-0">
            {!isEditingNewTable ? (
                 <Button onClick={startNewTableMode} variant="secondary" size="md">
                   <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" viewBox="0 0 20 20" fill="currentColor"><path fillRule="evenodd" d="M10 3a1 1 0 011 1v4h4a1 1 0 110 2h-4v4a1 1 0 11-2 0v-4H5a1 1 0 110-2h4V4a1 1 0 011-1z" clipRule="evenodd" /></svg>
                   Add New Table
                 </Button>
            ) : (
                <Button onClick={cancelNewTableMode} variant="ghost" size="md">Cancel</Button>
            )}
        </div>
      </div>

      {(selectedTableName || isEditingNewTable) && editableEntries.length > 0 && (
        <div className="overflow-x-auto perspective-racing-input-bg rounded-lg shadow">
          <table className="min-w-full divide-y perspective-racing-border">
            <thead className="bg-slate-700"> {/* Darker shade for thead */}
              <tr>
                <th className="px-3 py-3 text-left text-xs font-medium perspective-racing-text-secondary uppercase tracking-wider">TWS (kts)</th>
                <th className="px-3 py-3 text-left text-xs font-medium perspective-racing-text-secondary uppercase tracking-wider">TWA (°)</th>
                <th className="px-3 py-3 text-left text-xs font-medium perspective-racing-text-secondary uppercase tracking-wider">Target Speed (kts)</th>
                <th className="px-3 py-3 text-left text-xs font-medium perspective-racing-text-secondary uppercase tracking-wider">Actions</th>
              </tr>
            </thead>
            <tbody className="perspective-racing-card-bg divide-y perspective-racing-border"> {/* Use card-bg or input-bg */}
              {editableEntries.map((entry, index) => (
                <tr key={index} className="hover:bg-slate-700/70 transition-colors">
                  <td className="px-3 py-2 whitespace-nowrap"><Input type="number" step="0.1" min="0" value={entry.tws} onChange={(e) => handleEntryChange(index, 'tws', e.target.value)} className="w-28 py-1.5 text-sm"/></td>
                  <td className="px-3 py-2 whitespace-nowrap"><Input type="number" step="1" min="0" max="180" value={entry.twa} onChange={(e) => handleEntryChange(index, 'twa', e.target.value)} className="w-28 py-1.5 text-sm"/></td>
                  <td className="px-3 py-2 whitespace-nowrap"><Input type="number" step="0.01" min="0" value={entry.targetSpeed} onChange={(e) => handleEntryChange(index, 'targetSpeed', e.target.value)} className="w-32 py-1.5 text-sm"/></td>
                  <td className="px-3 py-2 whitespace-nowrap">
                    <Button onClick={() => removeEntryRow(index)} variant="danger" size="sm">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor"><path fillRule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clipRule="evenodd" /></svg>
                    </Button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
      {(selectedTableName || isEditingNewTable) && (
        <div className="mt-6 flex flex-col sm:flex-row justify-between items-center gap-4 p-4 perspective-racing-input-bg rounded-lg shadow">
            <Button onClick={addEntryRow} variant="secondary" size="md">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" viewBox="0 0 20 20" fill="currentColor"><path d="M5 3a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2V5a2 2 0 00-2-2H5zM5 11a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2v-2a2 2 0 00-2-2H5zM11 5a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V5zM13 11a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2v-2a2 2 0 00-2-2h-2z" /></svg>
              Add Data Row
            </Button>
            <Button onClick={handleSubmit} isLoading={isSubmitting} size="md" variant="primary" disabled={editableEntries.length === 0}>
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" viewBox="0 0 20 20" fill="currentColor"><path d="M7.707 10.293a1 1 0 10-1.414 1.414l3 3a1 1 0 001.414 0l3-3a1 1 0 00-1.414-1.414L11 11.586V6h5a1 1 0 011 1v3a1 1 0 11-2 0V8h-3v3.586L7.707 10.293zM5 3a1 1 0 011-1h8a1 1 0 011 1v3h-2V4H6v2H4V3z" /></svg>
                {isSubmitting ? 'Saving...' : (isEditingNewTable ? 'Create Polar Table' : 'Save Changes to Table')}
            </Button>
        </div>
      )}
      {message && <p className={`text-sm mt-3 p-3 rounded-md ${message.type === 'success' ? 'bg-green-700/30 text-green-300' : 'bg-red-700/30 text-red-300'}`}>{message.text}</p>}
      {polarTables.length === 0 && !isEditingNewTable && !loading && (
        <div className="text-center py-8 px-4 perspective-racing-input-bg rounded-lg shadow">
            <p className="mb-2 perspective-racing-text-secondary">No polar tables found.</p>
            <Button onClick={startNewTableMode} variant="primary">Create Your First Polar Table</Button>
        </div>
      )}
    </div>
  );
};

export default PolarTableEditor;