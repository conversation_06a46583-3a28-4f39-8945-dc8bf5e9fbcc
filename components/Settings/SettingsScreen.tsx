import React from 'react';
import SmoothingSettings from './SmoothingSettings';
import PolarTableEditor from './PolarTableEditor';
import Card from '../ui/Card';
import Button from '../ui/Button';

const SettingsScreen: React.FC = () => {
  const handleImportClick = () => {
    document.getElementById('import-polar-file')?.click();
  };
  
  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files && event.target.files[0]) {
      alert(`Importing ${event.target.files[0].name} (conceptual).`);
    }
  };


  return (
    <div className="space-y-8">
      <Card title="Performance Calculation Settings">
        <SmoothingSettings />
      </Card>
      
      <Card title="Polar Performance Tables">
        <PolarTableEditor />
      </Card>

      <Card title="Application Data & Debugging">
        <div className="space-y-6">
          <div>
            <h4 className="text-md font-medium perspective-racing-text-primary mb-2">NMEA Debug Logs</h4>
            <label htmlFor="nmea-debug" className="flex items-center space-x-3 cursor-pointer group">
              <input 
                type="checkbox" 
                id="nmea-debug" 
                className="appearance-none h-5 w-5 border-2 perspective-racing-border rounded perspective-racing-input-bg checked:bg-sky-600 checked:border-sky-600 focus:outline-none focus:ring-2 focus:ring-sky-500 focus:ring-offset-2 focus:ring-offset-[#131A2A] transition duration-150"
              />
              <span className="text-sm perspective-racing-text-secondary group-hover:perspective-racing-text-primary">Enable detailed NMEA sentence logging (requires backend support and may impact performance)</span>
            </label>
          </div>
          <div className="border-t perspective-racing-border pt-6">
             <h4 className="text-md font-medium perspective-racing-text-primary mb-1">Polar Data Management</h4>
             <p className="text-sm perspective-racing-text-secondary mb-3">Upload or download polar tables in JSON format. This allows for backup, sharing, or offline editing.</p>
             <div className="flex flex-col sm:flex-row gap-3">
                <input type="file" id="import-polar-file" accept=".json" className="hidden" onChange={handleFileChange} />
                <Button onClick={handleImportClick} variant="outline" size="md">
                   <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}><path strokeLinecap="round" strokeLinejoin="round" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12" /></svg>
                  Import Polar JSON
                </Button>
                <Button onClick={() => alert('Polar export initiated (conceptual).')} variant="outline" size="md">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}><path strokeLinecap="round" strokeLinejoin="round" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" /></svg>
                  Export Polar JSON
                </Button>
             </div>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default SettingsScreen;