
import React, { useState } from 'react';
import Input from '../ui/Input';
import Button from '../ui/Button';

interface SmoothingConfig {
  bspWindow: number;
  twsWindow: number;
  motionWindow: number; // For heel/pitch/roll -> sea state
}

const SmoothingSettings: React.FC = () => {
  const [config, setConfig] = useState<SmoothingConfig>({
    bspWindow: 5, // Default 5 seconds or samples
    twsWindow: 10,
    motionWindow: 30,
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [message, setMessage] = useState<string | null>(null);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setConfig({
      ...config,
      [e.target.name]: parseInt(e.target.value, 10) || 0,
    });
    setMessage(null);
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setMessage(null);
    // Simulate API call
    console.log("Updating smoothing settings:", config);
    // Assume services/api.ts handles the mock update
    // For example: mockUpdateSmoothingSettings(config).then(...)
    setTimeout(() => {
      setIsSubmitting(false);
      setMessage("Smoothing settings updated successfully!");
      setTimeout(() => setMessage(null), 4000);
    }, 1000);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <p className="text-sm text-gray-400">
        Adjust the window size (in seconds or number of samples, depending on backend) for Exponential Moving Average (EMA) or rolling window filters.
        Larger values provide smoother data but increase latency.
      </p>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Input
          label="Boat Speed (BSP) Window"
          id="bspWindow"
          name="bspWindow"
          type="number"
          value={config.bspWindow}
          onChange={handleChange}
          min="1"
          max="60"
        />
        <Input
          label="True Wind Speed (TWS) Window"
          id="twsWindow"
          name="twsWindow"
          type="number"
          value={config.twsWindow}
          onChange={handleChange}
          min="1"
          max="120"
        />
        <Input
          label="Motion Data (Sea State) Window"
          id="motionWindow"
          name="motionWindow"
          type="number"
          value={config.motionWindow}
          onChange={handleChange}
          min="5"
          max="300"
        />
      </div>
      <div className="flex justify-end">
        <Button type="submit" isLoading={isSubmitting}>
          {isSubmitting ? 'Saving...' : 'Save Smoothing Settings'}
        </Button>
      </div>
      {message && <p className={`text-sm mt-2 ${message.includes('successfully') ? 'text-green-400' : 'text-red-400'}`}>{message}</p>}
    </form>
  );
};

export default SmoothingSettings;