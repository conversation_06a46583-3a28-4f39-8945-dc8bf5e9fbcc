
import React, { useContext } from 'react';
import { LiveDataContext } from '../../contexts/LiveDataContext';

const WindyWidget: React.FC = () => {
  const { liveData } = useContext(LiveDataContext);
  // Default coordinates set to Burlington, Ontario if liveData is not available or doesn't have lat/lon
  const lat = liveData?.lat ?? 43.3259; 
  const lon = liveData?.lon ?? -79.7990;

  const windyUrl = `https://embed.windy.com/embed2.html?lat=${lat}&lon=${lon}&zoom=9&level=surface&overlay=wind&product=hrrr&menu=&message=&marker=&calendar=now&pressure=&type=map&location=coordinates&detail=&metricWind=kt&metricTemp=%C2%B0C&radarRange=-1`;

  return (
    <div className="aspect-w-16 aspect-h-9 rounded-lg overflow-hidden shadow-lg">
      <iframe
        width="100%"
        height="100%" // Tailwind aspect ratio handles height
        src={windyUrl}
        frameBorder="0"
        title="Windy.com Weather Widget"
        className="min-h-[300px] md:min-h-[400px]" // Minimum height for usability
      ></iframe>
    </div>
  );
};

export default WindyWidget;
