
import React from 'react';
import MapWithMarks from './MapWithMarks';
import WindyWidget from './WindyWidget';
import StartTimer from './StartTimer';
import Card from '../ui/Card';

const RacePlannerScreen: React.FC = () => {
  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2">
          <Card title="Race Course & Marks">
            <MapWithMarks />
          </Card>
        </div>
        <div className="space-y-6">
          <Card title="Weather Forecast (HRRR Model)">
            <WindyWidget />
          </Card>
          <Card title="Race Start Timer">
            <StartTimer raceEvent={null} />
          </Card>
        </div>
      </div>
    </div>
  );
};

export default RacePlannerScreen;
