
import React from 'react';
import MapWithMarks from './MapWithMarks';
import WindyWidget from './WindyWidget';
import StartTimer from './StartTimer';
import Card from '../ui/Card';

const RacePlannerScreen: React.FC = () => {
  // Mock selected race event for StartTimer
  const mockRaceEvent = {
    id: 'race1',
    name: 'Upcoming Regatta',
    startTime: new Date(Date.now() + 3 * 60 * 60 * 1000 + 15 * 60 * 1000), // 3 hours 15 mins from now
  };

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2">
          <Card title="Race Course & Marks">
            <MapWithMarks />
          </Card>
        </div>
        <div className="space-y-6">
          <Card title="Weather Forecast (HRRR Model)">
            <WindyWidget />
          </Card>
          <Card title="Race Start Timer">
            <StartTimer raceEvent={mockRaceEvent} />
          </Card>
        </div>
      </div>
    </div>
  );
};

export default RacePlannerScreen;
