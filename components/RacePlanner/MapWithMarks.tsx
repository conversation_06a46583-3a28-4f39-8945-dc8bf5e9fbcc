
import React, { useEffect, useRef, useContext, useState } from 'react';
import { LiveDataContext } from '../../contexts/LiveDataContext';

// Make Leaflet (L) available from window if loaded via CDN
declare const L: any; // Basic declaration for Leaflet global

const MapWithMarks: React.FC = () => {
  const mapContainerRef = useRef<HTMLDivElement>(null);
  const mapInstanceRef = useRef<any>(null); // To store the Leaflet map instance
  const boatMarkerRef = useRef<any>(null); // To store the boat marker instance
  const { liveData } = useContext(LiveDataContext);
  const [isLeafletReady, setIsLeafletReady] = useState(false);
  const [mapInitialized, setMapInitialized] = useState(false);

  useEffect(() => {
    // Check if Leaflet is loaded
    if (typeof L !== 'undefined') {
      setIsLeafletReady(true);
    } else {
      // Poll for Leaflet availability if not immediately ready
      const intervalId = setInterval(() => {
        if (typeof L !== 'undefined') {
          setIsLeafletReady(true);
          clearInterval(intervalId);
        }
      }, 100);
      return () => clearInterval(intervalId);
    }
  }, []);

  // Effect for initializing the map
  useEffect(() => {
    if (isLeafletReady && mapContainerRef.current && !mapInstanceRef.current) {
      const defaultLat = liveData?.lat ?? 34.0522; // Los Angeles
      const defaultLon = liveData?.lon ?? -118.2437;
      const defaultZoom = liveData?.lat ? 13 : 9;

      try {
        const map = L.map(mapContainerRef.current).setView([defaultLat, defaultLon], defaultZoom);
        mapInstanceRef.current = map;

        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
          attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
          maxZoom: 19,
        }).addTo(map);
        
        // Invalidate size after a short delay to ensure proper rendering
        setTimeout(() => {
          map.invalidateSize();
          setMapInitialized(true); // Mark map as initialized AFTER invalidateSize
        }, 150); // Increased delay slightly

      } catch (error) {
        console.error("Error initializing Leaflet map:", error);
        if (mapContainerRef.current) {
            mapContainerRef.current.innerHTML = '<p class="text-red-400 text-center p-4">Could not load map. Please try refreshing.</p>';
        }
      }
    }

    // Cleanup function: remove map instance when component unmounts
    return () => {
      if (mapInstanceRef.current) {
        mapInstanceRef.current.remove();
        mapInstanceRef.current = null;
        boatMarkerRef.current = null;
        setMapInitialized(false);
      }
    };
  }, [isLeafletReady]); // Removed liveData from dependencies

  // Effect for updating boat marker and view based on liveData
  useEffect(() => {
    if (mapInstanceRef.current && mapInitialized && liveData?.lat && liveData?.lon) {
      const map = mapInstanceRef.current;
      const currentLatLng = L.latLng(liveData.lat, liveData.lon);

      if (boatMarkerRef.current) {
        boatMarkerRef.current.setLatLng(currentLatLng);
      } else {
        // Create a simple boat icon (SVG or character)
        const boatIconHtml = `<div style="font-size: 24px; transform: rotate(${liveData.heading || 0}deg);">⛵</div>`;
        const boatIcon = L.divIcon({
            className: 'leaflet-boat-icon',
            html: boatIconHtml,
            iconSize: [24, 24],
            iconAnchor: [12, 12] // Center of the icon
        });
        boatMarkerRef.current = L.marker(currentLatLng, { icon: boatIcon }).addTo(map)
          .bindPopup('Current Position');
      }
      
      // Optionally, pan or set view if desired, but be mindful of user experience
      // map.panTo(currentLatLng); // Example: pan to new location
      // map.setView(currentLatLng, map.getZoom()); // Example: center view, keep zoom
    }
  }, [liveData?.lat, liveData?.lon, liveData?.heading, mapInitialized, isLeafletReady]);


  if (!isLeafletReady) {
    return (
      <div className="h-96 md:h-[500px] perspective-racing-input-bg rounded-lg flex items-center justify-center p-4 border-2 border-dashed perspective-racing-border">
        <svg className="animate-spin h-8 w-8 perspective-racing-accent-sky mr-3" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
        <p className="perspective-racing-text-secondary">Loading Map Library...</p>
      </div>
    );
  }

  return (
    <div className="h-96 md:h-[500px] perspective-racing-input-bg rounded-lg shadow-inner relative">
      <div ref={mapContainerRef} className="w-full h-full rounded-lg" />
       {/* Placeholder for map controls if needed later */}
       {/* <div className="absolute top-3 right-3 z-[1000] space-y-2"></div> */}
    </div>
  );
};

export default MapWithMarks;
