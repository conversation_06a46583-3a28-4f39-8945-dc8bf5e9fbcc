import React, { useState, useEffect } from 'react';
import { RaceEvent } from '../../types';

interface StartTimerProps {
  raceEvent: RaceEvent | null;
}

const StartTimer: React.FC<StartTimerProps> = ({ raceEvent }) => {
  const [timeLeft, setTimeLeft] = useState<string>("--:--:--");
  const [timeParts, setTimeParts] = useState<{h:number,m:number,s:number} | null>(null);
  const [isCounting, setIsCounting] = useState<boolean>(false);
  const [isImminent, setIsImminent] = useState<boolean>(false);
  const [isVeryImminent, setIsVeryImminent] = useState<boolean>(false);

  useEffect(() => {
    if (!raceEvent || !raceEvent.startTime) {
      setIsCounting(false);
      setTimeLeft("--:--:--");
      setTimeParts(null);
      setIsImminent(false);
      setIsVeryImminent(false);
      return;
    }

    const calculateTimeLeft = () => {
      const difference = +new Date(raceEvent.startTime) - +new Date();
      let newTimeLeft = "--:--:--";
      let currentParts = null;
      let counting = false;
      let imminent = false;
      let veryImminent = false;

      if (difference > 0) {
        counting = true;
        const totalSeconds = Math.floor(difference / 1000);
        const hours = Math.floor(totalSeconds / 3600);
        const minutes = Math.floor((totalSeconds % 3600) / 60);
        const seconds = totalSeconds % 60;
        
        currentParts = {h: hours, m: minutes, s: seconds};

        newTimeLeft = 
          (hours > 0 ? `${String(hours).padStart(2, '0')}:` : "") +
          `${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}`;
        
        if (totalSeconds <= 300) imminent = true; // Under 5 minutes
        if (totalSeconds <= 60) veryImminent = true; // Under 1 minute

      } else {
        newTimeLeft = "STARTED";
      }
      setTimeLeft(newTimeLeft);
      setTimeParts(currentParts);
      setIsCounting(counting);
      setIsImminent(imminent);
      setIsVeryImminent(veryImminent);
    };

    calculateTimeLeft(); 
    const timerInterval = setInterval(calculateTimeLeft, 1000);

    return () => clearInterval(timerInterval);
  }, [raceEvent]);

  if (!raceEvent) {
    return <div className="text-center text-gray-400 p-4">No race selected for timer.</div>;
  }

  let timerColor = 'text-gray-100';
  if (isCounting) {
    if (isVeryImminent) timerColor = 'text-red-400 animate-pulse'; 
    else if (isImminent) timerColor = 'text-amber-400'; 
  }


  return (
    <div className="text-center p-4 flex flex-col items-center justify-center h-full">
      <div className="text-sm text-gray-400 mb-1 truncate max-w-full px-2">
        Countdown to: <span className="font-semibold text-gray-200">{raceEvent.name}</span>
      </div>
      <div className={`text-4xl md:text-5xl font-mono font-bold tracking-wider ${timerColor} my-2`}>
        {timeLeft}
      </div>
      <div className="text-xs text-gray-500">
        Start: {new Date(raceEvent.startTime).toLocaleDateString([], { weekday: 'short', month: 'short', day: 'numeric'})}, {new Date(raceEvent.startTime).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})}
      </div>
    </div>
  );
};

export default StartTimer;