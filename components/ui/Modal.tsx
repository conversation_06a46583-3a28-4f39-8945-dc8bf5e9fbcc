import React, { ReactNode } from 'react';

interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  children: ReactNode;
  footer?: ReactNode;
  size?: 'sm' | 'md' | 'lg' | 'xl';
}

const Modal: React.FC<ModalProps> = ({ isOpen, onClose, title, children, footer, size = 'md' }) => {
  if (!isOpen) return null;

  let sizeClasses = 'max-w-lg'; // Default to md
  switch (size) {
    case 'sm': sizeClasses = 'max-w-sm'; break;
    case 'md': sizeClasses = 'max-w-md'; break; 
    case 'lg': sizeClasses = 'max-w-lg'; break;
    case 'xl': sizeClasses = 'max-w-xl'; break;
  }
  
  return (
    <div 
      className="fixed inset-0 bg-slate-900/80 backdrop-blur-sm flex items-center justify-center p-4 z-50 transition-opacity duration-300 ease-in-out"
      onClick={onClose}
    >
      <div 
        className={`perspective-racing-card-bg rounded-lg shadow-xl w-full ${sizeClasses} flex flex-col max-h-[90vh] transform transition-all duration-300 ease-in-out scale-95 opacity-0 animate-modalShow`}
        onClick={(e) => e.stopPropagation()} 
        style={{ animationFillMode: 'forwards' }} 
      >
        {title && (
          <div className="flex items-center justify-between px-6 py-4 border-b perspective-racing-border">
            <h3 className="text-xl font-semibold perspective-racing-text-primary">{title}</h3>
            <button
              onClick={onClose}
              className="perspective-racing-text-secondary hover:perspective-racing-text-primary transition-colors"
              aria-label="Close modal"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12"></path></svg>
            </button>
          </div>
        )}
        <div className="px-6 py-5 flex-grow overflow-y-auto">
          {children}
        </div>
        {footer && (
          <div className={`px-6 py-4 border-t perspective-racing-border perspective-racing-card-bg rounded-b-lg flex justify-end space-x-3`}>
            {footer}
          </div>
        )}
      </div>
      <style>{`
        @keyframes modalShow {
          to {
            opacity: 1;
            transform: scale(1);
          }
        }
        .animate-modalShow {
          animation-name: modalShow;
        }
      `}</style>
    </div>
  );
};

export default Modal;