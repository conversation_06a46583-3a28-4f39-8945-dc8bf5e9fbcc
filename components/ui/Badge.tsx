
import React from 'react';

interface BadgeProps {
  children: React.ReactNode;
  color?: 'sky' | 'teal' | 'green' | 'amber' | 'red' | 'gray';
  size?: 'sm' | 'md';
  className?: string;
}

const Badge: React.FC<BadgeProps> = ({ children, color = 'sky', size = 'md', className = '' }) => {
  let colorClasses = '';
  switch (color) {
    case 'teal':
      colorClasses = 'bg-teal-600 text-teal-100';
      break;
    case 'green':
      colorClasses = 'bg-green-600 text-green-100';
      break;
    case 'amber':
      colorClasses = 'bg-amber-500 text-amber-900';
      break;
    case 'red':
      colorClasses = 'bg-red-600 text-red-100';
      break;
    case 'gray':
      colorClasses = 'bg-gray-600 text-gray-100';
      break;
    case 'sky':
    default:
      colorClasses = 'bg-sky-600 text-sky-100';
      break;
  }

  let sizeClasses = '';
  switch (size) {
    case 'sm':
      sizeClasses = 'px-2 py-0.5 text-xs';
      break;
    case 'md':
    default:
      sizeClasses = 'px-2.5 py-0.5 text-sm';
      break;
  }

  return (
    <span className={`inline-flex items-center font-medium rounded-full ${colorClasses} ${sizeClasses} ${className}`}>
      {children}
    </span>
  );
};

export default Badge;
