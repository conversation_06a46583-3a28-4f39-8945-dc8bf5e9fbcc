
import React from 'react';

interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'danger' | 'ghost' | 'outline';
  size?: 'sm' | 'md' | 'lg' | 'icon';
  isLoading?: boolean;
  children: React.ReactNode;
  asChild?: boolean; // For potential future use with Slot component
}

const Button: React.FC<ButtonProps> = ({ 
  variant = 'primary', 
  size = 'md', 
  isLoading = false, 
  children, 
  className = '', 
  asChild = false, // Currently not implemented with Slot, but prop is there
  disabled,
  ...props 
}) => {
  const baseClasses = "inline-flex items-center justify-center rounded-md font-semibold focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-gray-900 transition-all duration-150 ease-in-out disabled:opacity-60 disabled:cursor-not-allowed shadow-sm hover:shadow-md";

  let variantClasses = '';
  switch (variant) {
    case 'primary':
      variantClasses = 'bg-sky-500 hover:bg-sky-600 text-white focus:ring-sky-400 border border-sky-500';
      break;
    case 'secondary':
      variantClasses = 'bg-slate-600 hover:bg-slate-700 text-slate-100 focus:ring-slate-500 border border-slate-600';
      break;
    case 'danger':
      variantClasses = 'bg-red-600 hover:bg-red-700 text-white focus:ring-red-500 border border-red-600';
      break;
    case 'ghost':
      variantClasses = 'bg-transparent hover:bg-slate-700/70 text-slate-200 focus:ring-slate-500 border border-transparent';
      break;
    case 'outline':
      variantClasses = 'bg-transparent border border-slate-500 hover:bg-slate-700/50 text-slate-200 focus:ring-slate-400';
      break;
  }

  let sizeClasses = '';
  switch (size) {
    case 'sm':
      sizeClasses = 'px-3 py-1.5 text-xs';
      break;
    case 'md':
      sizeClasses = 'px-4 py-2 text-sm';
      break;
    case 'lg':
      sizeClasses = 'px-6 py-2.5 text-base';
      break;
    case 'icon':
      sizeClasses = 'p-2 aspect-square'; // Ensure it's square for icons
      break;
  }

  // If asChild is true, we'd render React.Children.only(children) with merged props.
  // For now, it's a standard button.
  if (asChild) {
    // Placeholder for Slot implementation if needed in the future
    console.warn("Button 'asChild' prop is set, but Slot functionality is not fully implemented in this version.");
  }

  return (
    <button
      type="button"
      className={`${baseClasses} ${variantClasses} ${sizeClasses} ${className}`}
      disabled={isLoading || disabled}
      {...props}
    >
      {isLoading && (
        <svg className="animate-spin -ml-0.5 mr-2 h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="3"></circle>
          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
      )}
      {children}
    </button>
  );
};

export default Button;
