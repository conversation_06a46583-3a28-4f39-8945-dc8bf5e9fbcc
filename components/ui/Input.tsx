import React from 'react';

interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: string;
  error?: string;
  icon?: React.ReactNode;
  containerClassName?: string;
}

const Input: React.FC<InputProps> = ({ label, id, error, icon, className, containerClassName, ...props }) => {
  const baseInputClasses = "block w-full h-10 px-3 py-2 perspective-racing-input-bg border perspective-racing-border rounded-md shadow-sm placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-sky-500 focus:border-sky-500 sm:text-sm perspective-racing-text-primary transition-colors duration-150";
  const iconPadding = icon ? "pl-10" : "";

  return (
    <div className={`w-full ${containerClassName}`}>
      {label && (
        <label htmlFor={id} className="block text-sm font-medium perspective-racing-text-secondary mb-1.5">
          {label}
        </label>
      )}
      <div className="relative rounded-md shadow-sm">
        {icon && (
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <span className="text-slate-400 sm:text-sm h-5 w-5">{icon}</span>
          </div>
        )}
        <input
          id={id}
          className={`${baseInputClasses} ${iconPadding} ${error ? 'border-red-500 focus:ring-red-500 focus:border-red-500' : 'perspective-racing-border'} ${className}`}
          {...props}
        />
      </div>
      {error && <p className="mt-1.5 text-xs text-red-400">{error}</p>}
    </div>
  );
};

export default Input;