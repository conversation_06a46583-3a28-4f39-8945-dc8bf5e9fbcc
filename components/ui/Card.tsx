import React from 'react';

interface CardProps {
  children: React.ReactNode;
  className?: string;
  title?: string;
  titleAction?: React.ReactNode; // New prop for actions in the title bar
  footer?: React.ReactNode;
}

const Card: React.FC<CardProps> = ({ children, className = '', title, titleAction, footer }) => {
  return (
    <div className={`perspective-racing-card-bg shadow-xl rounded-lg overflow-hidden ${className}`}>
      {title && (
        <div className={`px-4 py-4 sm:px-6 perspective-racing-card-bg border-b perspective-racing-border flex justify-between items-center`}> {/* Slightly darker or same as card bg */}
          <h3 className="text-lg leading-6 font-semibold perspective-racing-text-primary">{title}</h3>
          {titleAction && <div>{titleAction}</div>}
        </div>
      )}
      <div className="p-4 sm:p-6">
        {children}
      </div>
      {footer && (
        <div className={`px-4 py-4 sm:px-6 perspective-racing-card-bg border-t perspective-racing-border`}> {/* Slightly darker or same as card bg */}
          {footer}
        </div>
      )}
    </div>
  );
};

export default Card;