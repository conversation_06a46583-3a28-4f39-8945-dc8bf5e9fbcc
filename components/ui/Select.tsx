import React from 'react';

interface SelectProps extends React.SelectHTMLAttributes<HTMLSelectElement> {
  label?: string;
  error?: string;
  options: Array<{ value: string | number; label: string }>;
  placeholder?: string; 
  containerClassName?: string;
}

const Select: React.FC<SelectProps> = ({ label, id, error, options, className, placeholder, containerClassName, ...props }) => {
  const baseSelectClasses = "appearance-none block w-full h-10 pl-3 pr-10 py-2 perspective-racing-input-bg border perspective-racing-border rounded-md shadow-sm placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-sky-500 focus:border-sky-500 sm:text-sm perspective-racing-text-primary transition-colors duration-150";
  
  return (
    <div className={`w-full ${containerClassName}`}>
      {label && (
        <label htmlFor={id} className="block text-sm font-medium perspective-racing-text-secondary mb-1.5">
          {label}
        </label>
      )}
      <div className="relative">
        <select
          id={id}
          className={`${baseSelectClasses} ${error ? 'border-red-500 focus:ring-red-500 focus:border-red-500' : 'perspective-racing-border'} ${className}`}
          {...props}
        >
          {placeholder && <option value="" disabled className="perspective-racing-input-bg perspective-racing-text-secondary">{placeholder}</option>}
          {options.map(option => (
            <option key={option.value} value={option.value} className="perspective-racing-input-bg perspective-racing-text-primary hover:bg-slate-600">
              {option.label}
            </option>
          ))}
        </select>
        <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-slate-400">
          <svg className="h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
            <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
          </svg>
        </div>
      </div>
      {error && <p className="mt-1.5 text-xs text-red-400">{error}</p>}
    </div>
  );
};

export default Select;