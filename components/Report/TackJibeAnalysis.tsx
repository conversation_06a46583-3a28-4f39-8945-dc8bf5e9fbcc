
import React, { useState, useEffect } from 'react';
import { TackJibeInfo } from '../../types';
import { fetchTackJibeAnalysis } from '../../services/api';

interface TackJibeAnalysisProps {
  raceId: string;
}

const LoadingState: React.FC<{ message: string }> = ({ message }) => (
  <div className="flex flex-col items-center justify-center py-10 perspective-racing-text-secondary">
    <svg className="animate-spin h-8 w-8 perspective-racing-accent-sky mb-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
    </svg>
    {message}
  </div>
);

const TackJibeAnalysis: React.FC<TackJibeAnalysisProps> = ({ raceId }) => {
  const [analysisData, setAnalysisData] = useState<TackJibeInfo[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadAnalysis = async () => {
      if (!raceId) {
        setAnalysisData([]);
        setLoading(false);
        return;
      }
      setLoading(true);
      setError(null);
      try {
        const data = await fetchTackJibeAnalysis(raceId);
        setAnalysisData(data);
// Fix: Remove extraneous underscore from catch block and ensure 'err' is correctly referenced.
      } catch (err) {
        console.error("Failed to load tack/jibe analysis:", err);
        setError("Could not load maneuver data for this race.");
      } finally {
        setLoading(false);
      }
    };
    loadAnalysis();
  }, [raceId]);

  if (loading) return <LoadingState message="Loading maneuver analysis..." />;
  if (error) return <div className="text-red-400 py-10 text-center">{error}</div>;
  if (analysisData.length === 0) return <div className="perspective-racing-text-secondary py-10 text-center">No maneuver data available for this race.</div>;

  return (
    <div className="overflow-x-auto perspective-racing-input-bg p-1 rounded-lg shadow-md"> {/* Changed background */}
      <table className="min-w-full divide-y perspective-racing-border">
        <thead className="bg-slate-700 sticky top-0 z-10"> {/* Darker shade for thead */}
          <tr>
            {['Time', 'Type', 'Duration', 'Entry Spd', 'Exit Spd', 'Avg Spd', 'Dist. Lost'].map(header => (
              <th key={header} scope="col" className="px-4 py-3.5 text-left text-xs font-semibold perspective-racing-text-secondary uppercase tracking-wider whitespace-nowrap">
                {header}
                {header.includes('Spd') && <span className="text-slate-500 normal-case"> (kts)</span>}
                {header.includes('Duration') && <span className="text-slate-500 normal-case"> (s)</span>}
                {header.includes('Dist.') && <span className="text-slate-500 normal-case"> (m)</span>}
              </th>
            ))}
          </tr>
        </thead>
        <tbody className="perspective-racing-card-bg divide-y perspective-racing-border"> {/* Use card-bg or input-bg for rows */}
          {analysisData.map((item) => (
            <tr key={item.id} className="hover:bg-slate-700 transition-colors duration-100">
              <td className="px-4 py-3 whitespace-nowrap text-sm perspective-racing-text-primary">{new Date(item.startTime).toLocaleTimeString([], {hour: '2-digit', minute: '2-digit', second: '2-digit'})}</td>
              <td className="px-4 py-3 whitespace-nowrap text-sm">
                <span className={`px-2.5 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${item.type === 'Tack' ? 'bg-sky-200 text-sky-800' : 'bg-teal-200 text-teal-800'}`}>
                  {item.type}
                </span>
              </td>
              <td className="px-4 py-3 whitespace-nowrap text-sm perspective-racing-text-primary text-right">{item.durationSeconds.toFixed(1)}</td>
              <td className="px-4 py-3 whitespace-nowrap text-sm perspective-racing-text-primary text-right">{item.entrySpeed.toFixed(1)}</td>
              <td className="px-4 py-3 whitespace-nowrap text-sm perspective-racing-text-primary text-right">{item.exitSpeed.toFixed(1)}</td>
              <td className="px-4 py-3 whitespace-nowrap text-sm perspective-racing-text-primary text-right">{item.avgSpeedDuringManeuver.toFixed(1)}</td>
              <td className="px-4 py-3 whitespace-nowrap text-sm perspective-racing-text-primary text-right">{item.distanceLostMeters?.toFixed(1) ?? 'N/A'}</td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

export default TackJibeAnalysis;