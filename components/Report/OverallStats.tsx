import React, { useState, useEffect } from 'react';
import { fetchPerformanceReportSummary } from '../../services/api';

interface OverallStatsProps {
  raceId: string;
}

interface StatsData {
  avgBoatSpeed: number;
  maxBoatSpeed: number;
  avgPercentPolar: number;
  timeAtOptimal: string;
  totalDistance: number;
  tacks: number;
  jibes: number;
}

const StatItem: React.FC<{ label: string; value: string | number; unit?: string; className?: string }> = ({ label, value, unit, className }) => (
  <div className={`perspective-racing-input-bg p-4 rounded-lg shadow-md hover:bg-slate-700 transition-colors duration-150 ${className}`}> {/* Changed background */}
    <dt className="text-sm font-medium perspective-racing-text-secondary truncate">{label}</dt>
    <dd className="mt-1 text-3xl font-semibold perspective-racing-accent-sky">
      {value}
      {unit && <span className="ml-1.5 text-lg font-normal perspective-racing-text-primary">{unit}</span>}
    </dd>
  </div>
);

const LoadingState: React.FC<{ message: string }> = ({ message }) => (
  <div className="flex flex-col items-center justify-center py-10 perspective-racing-text-secondary col-span-full">
    <svg className="animate-spin h-8 w-8 perspective-racing-accent-sky mb-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
    </svg>
    {message}
  </div>
);

const OverallStats: React.FC<OverallStatsProps> = ({ raceId }) => {
  const [stats, setStats] = useState<StatsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadStats = async () => {
      if (!raceId) {
        setStats(null); 
        setLoading(false);
        return;
      }
      setLoading(true);
      setError(null);
      try {
        const summary = await fetchPerformanceReportSummary(raceId);
        setStats(summary);
      } catch (err) {
        console.error("Failed to load overall stats:", err);
        setError("Could not load summary data for this race.");
      } finally {
        setLoading(false);
      }
    };
    loadStats();
  }, [raceId]);

  if (loading) return <LoadingState message="Loading overall stats..." />;
  if (error) return <div className="text-red-400 py-10 text-center col-span-full">{error}</div>;
  if (!stats) return <div className="perspective-racing-text-secondary py-10 text-center col-span-full">No summary data available for the selected race.</div>;

  return (
    <dl className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
      <StatItem label="Avg. Boat Speed" value={stats.avgBoatSpeed.toFixed(1)} unit="kts" />
      <StatItem label="Max Boat Speed" value={stats.maxBoatSpeed.toFixed(1)} unit="kts" />
      <StatItem label="Avg. % Polar" value={stats.avgPercentPolar.toFixed(1)} unit="%" />
      <StatItem label="Time at Optimal Perf." value={stats.timeAtOptimal} />
      <StatItem label="Total Distance" value={stats.totalDistance.toFixed(1)} unit="NM" />
      <StatItem label="Tacks" value={stats.tacks} />
      <StatItem label="Jibes" value={stats.jibes} />
    </dl>
  );
};

export default OverallStats;