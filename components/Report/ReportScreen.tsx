import React, { useState } from 'react';
import OverallStats from './OverallStats';
import TimeChart from './TimeChart';
import TackJibeAnalysis from './TackJibeAnalysis';
import ExportButton from './ExportButton';
import Card from '../ui/Card';
import Select from '../ui/Select';
import { fetchRaceEvents } from '../../services/api'; 
import { RaceEvent } from '../../types';
import Button from '../ui/Button'; 

const REPORT_CONTENT_ID = "report-content-area";

const ReportScreen: React.FC = () => {
  const [selectedRaceId, setSelectedRaceId] = useState<string | null>(null);
  const [raceEvents, setRaceEvents] = useState<RaceEvent[]>([]);
  const [isLoadingRaces, setIsLoadingRaces] = useState(true);

  React.useEffect(() => {
    const loadRaces = async () => {
      try {
        setIsLoadingRaces(true);
        const events = await fetchRaceEvents();
        setRaceEvents(events.sort((a,b) => new Date(b.startTime).getTime() - new Date(a.startTime).getTime())); 
        if (events.length > 0) {
          setSelectedRaceId(events[0].id);
        }
      } catch (error) {
        console.error("Failed to load race events for report:", error);
      } finally {
        setIsLoadingRaces(false);
      }
    };
    loadRaces();
  }, []);

  const raceOptions = raceEvents.map(race => ({ 
    value: race.id, 
    label: `${race.name} (${new Date(race.startTime).toLocaleDateString()})` 
  }));

  return (
    <div className="space-y-6">
      <Card 
        title="Performance Report"
        titleAction={selectedRaceId && <ExportButton targetId={REPORT_CONTENT_ID} fileName={`PerspectiveRacing_Report_${selectedRaceId}.png`} />}
      >
        {isLoadingRaces ? (
          <div className="flex items-center justify-center py-6 perspective-racing-text-secondary">
            <svg className="animate-spin -ml-1 mr-3 h-5 w-5 perspective-racing-accent-sky" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            Loading races...
          </div>
        ) : raceOptions.length > 0 ? (
          <Select
            id="raceSelector"
            options={raceOptions}
            value={selectedRaceId || ''}
            onChange={(e) => setSelectedRaceId(e.target.value)}
            label="Select a race to analyze:"
            placeholder="Choose a race..."
            containerClassName="max-w-md"
          />
        ) : (
          <p className="perspective-racing-text-secondary py-4">No races available for reporting. Sail more to see reports!</p>
        )}
      </Card>

      {selectedRaceId && (
        <div id={REPORT_CONTENT_ID} className="space-y-6 perspective-racing-app-bg rounded-lg"> {/* Use app-bg for consistency or card-bg if it's a distinct area */}
          <Card title="Overall Performance Summary" className="report-section">
            <OverallStats raceId={selectedRaceId} />
          </Card>

          <Card title="Performance Over Time" className="report-section">
            <TimeChart raceId={selectedRaceId} />
          </Card>

          <Card title="Maneuver Analysis (Tacks & Jibes)" className="report-section">
            <TackJibeAnalysis raceId={selectedRaceId} />
          </Card>
        </div>
      )}
       {!selectedRaceId && !isLoadingRaces && raceOptions.length > 0 && (
         <Card>
            <p className="perspective-racing-text-secondary text-center py-8">Please select a race from the dropdown above to view its detailed performance report.</p>
         </Card>
       )}
    </div>
  );
};

export default ReportScreen;