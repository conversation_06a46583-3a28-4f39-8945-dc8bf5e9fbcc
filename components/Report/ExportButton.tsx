
import React, { useState } from 'react';
import Button from '../ui/Button';

// Make html2canvas available from window if loaded via CDN
const html2canvas: any = (window as any).html2canvas;

interface ExportButtonProps {
  targetId: string; // ID of the element to capture
  fileName?: string;
}

const ExportButton: React.FC<ExportButtonProps> = ({ targetId, fileName = "report_export.png" }) => {
  const [isLoading, setIsLoading] = useState(false);

  const handleExport = async () => {
    if (!html2canvas) {
      alert("Export functionality is currently unavailable. html2canvas library not loaded.");
      console.error("html2canvas not found.");
      return;
    }

    const elementToCapture = document.getElementById(targetId);
    if (!elementToCapture) {
      alert(`Error: Could not find element with ID "${targetId}" to export.`);
      console.warn(`No element found for ID: ${targetId}`);
      return;
    }

    setIsLoading(true);
    try {
      const canvas = await html2canvas(elementToCapture, { 
        useCORS: true, // For images from other origins if any
        scale: 2, // Increase resolution
        backgroundColor: '#111827' // Match bg-gray-900 for transparent areas
      });
      const image = canvas.toDataURL('image/png');
      const link = document.createElement('a');
      link.href = image;
      link.download = fileName;
      document.body.appendChild(link); // Required for Firefox
      link.click();
      document.body.removeChild(link);
    } catch (error) {
      console.error("Error during export:", error);
      alert("An error occurred while exporting the report.");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Button onClick={handleExport} variant="secondary" isLoading={isLoading}>
      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}>
        <path strokeLinecap="round" strokeLinejoin="round" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
      </svg>
      {isLoading ? 'Exporting...' : 'Export Report'}
    </Button>
  );
};

export default ExportButton;