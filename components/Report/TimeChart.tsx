import React, { useState, useEffect, useContext } from 'react';
import { PerformanceDataPoint } from '../../types';
import { fetchPerformanceDataForRace } from '../../services/api';
import { PolarContext } from '../../contexts/PolarContext';

const Recharts = (window as any).Recharts;

interface TimeChartProps {
  raceId: string;
}

type ChartMetric = 'bsp' | 'tws' | 'vmg' | 'percentPolar' | 'heel';

const LoadingState: React.FC<{ message: string }> = ({ message }) => (
  <div className="flex flex-col items-center justify-center h-full perspective-racing-text-secondary">
    <svg className="animate-spin h-8 w-8 perspective-racing-accent-sky mb-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
    </svg>
    {message}
  </div>
);

const metricOptions: { value: ChartMetric; label: string, unit: string, targetKey?: string }[] = [
  { value: 'vmg', label: 'VMG', unit: 'kts', targetKey: 'targetVMG' },
  { value: 'bsp', label: 'Boat Speed', unit: 'kts', targetKey: 'targetBoatSpeed' },
  { value: 'percentPolar', label: '% Polar', unit: '%', targetKey: 'targetForMetric' },
  { value: 'tws', label: 'True Wind Speed', unit: 'kts' },
  { value: 'heel', label: 'Heel Angle', unit: '°' },
];

const TimeChart: React.FC<TimeChartProps> = ({ raceId }) => {
  const [data, setData] = useState<PerformanceDataPoint[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedMetric, setSelectedMetric] = useState<ChartMetric>('vmg');
  const { getPolarTarget } = useContext(PolarContext);


  useEffect(() => {
    const loadData = async () => {
      if (!raceId) {
        setData([]);
        setLoading(false);
        return;
      }
      setLoading(true);
      setError(null);
      try {
        const performanceData = await fetchPerformanceDataForRace(raceId);
        const augmentedData = performanceData.map(dp => {
          const polarTarget = getPolarTarget(dp.tws, dp.twa);
          const targetBoatSpeed = polarTarget ? polarTarget.targetSpeed : dp.bsp * 0.9; 
          const targetVMGVal = targetBoatSpeed * Math.cos(dp.twa * Math.PI / 180);
          
          let targetForMetric;
          switch(selectedMetric) {
            case 'bsp': targetForMetric = targetBoatSpeed; break;
            case 'vmg': targetForMetric = targetVMGVal; break;
            case 'percentPolar': targetForMetric = 100; break;
            default: targetForMetric = undefined;
          }

          return {
            ...dp,
            time: new Date(dp.timestamp).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
            targetBoatSpeed: parseFloat(targetBoatSpeed.toFixed(1)),
            targetVMG: parseFloat(targetVMGVal.toFixed(1)),
            targetForMetric: targetForMetric !== undefined ? parseFloat(targetForMetric.toFixed(1)) : undefined,
          };
        });
        setData(augmentedData);
      } catch (err) {
        console.error("Failed to load time chart data:", err);
        setError("Could not load performance data for this race.");
      } finally {
        setLoading(false);
      }
    };
    loadData();
  }, [raceId, getPolarTarget, selectedMetric]);

  if (!Recharts) {
    return <div className="text-red-400 p-4 h-[450px] flex items-center justify-center">Recharts library not loaded. Please check CDN link.</div>;
  }
  const { ResponsiveContainer, LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend } = Recharts;

  const chartContent = () => {
    if (loading) return <LoadingState message="Loading chart data..." />;
    if (error) return <div className="text-red-400 h-full flex items-center justify-center">{error}</div>;
    if (data.length === 0) return <div className="perspective-racing-text-secondary h-full flex items-center justify-center">No performance data available for this race.</div>;

    const currentMetricConfig = metricOptions.find(m => m.value === selectedMetric);

    return (
      <ResponsiveContainer width="100%" height="100%">
        <LineChart data={data} margin={{ top: 5, right: 25, left: -5, bottom: 20 }}>
          <CartesianGrid strokeDasharray="3 3" stroke="rgba(51, 65, 85, 0.7)" /> {/* perspective-racing-border or similar */}
          <XAxis 
            dataKey="time" 
            stroke="#94A3B8" // perspective-racing-text-secondary
            tick={{ fontSize: 10, fill: '#94A3B8' }} 
            axisLine={{ stroke: '#334155' }} // perspective-racing-border
            tickLine={{ stroke: '#334155' }}
          />
          <YAxis 
            stroke="#94A3B8" 
            tick={{ fontSize: 10, fill: '#94A3B8' }} 
            axisLine={{ stroke: '#334155' }}
            tickLine={{ stroke: '#334155' }}
            label={{ value: currentMetricConfig?.unit, angle: -90, position: 'insideLeft', fill: '#94A3B8', dy: -10, dx: 10, fontSize: 12 }} 
            domain={selectedMetric === 'percentPolar' ? [70, 110] : ['auto', 'auto']}
          />
          <Tooltip 
            contentStyle={{ backgroundColor: 'rgba(30, 41, 59, 0.95)', border: '1px solid #334155', borderRadius: '0.375rem', boxShadow: '0 4px 6px rgba(0,0,0,0.1)' }} // perspective-racing-input-bg (approx)
            labelStyle={{ color: '#E0E0E0', fontWeight: '600', marginBottom: '4px', borderBottom: '1px solid #334155', paddingBottom: '4px' }} // perspective-racing-text-primary
            itemStyle={{ color: '#E0E0E0', paddingTop: '2px', paddingBottom: '2px' }} // perspective-racing-text-primary
            cursor={{ stroke: '#38BDF8', strokeWidth: 1, strokeDasharray: '3 3' }} // perspective-racing-accent-sky
          />
          <Legend 
            wrapperStyle={{paddingTop: '15px'}} 
            iconSize={10}
            formatter={(value) => <span style={{ color: '#E0E0E0', fontSize: '12px', marginLeft: '4px' }}>{value}</span>} // perspective-racing-text-primary
          />
          <Line type="monotone" dataKey={selectedMetric} name={currentMetricConfig?.label || 'Actual'} stroke="#38BDF8" strokeWidth={2.5} dot={false} activeDot={{ r: 5, strokeWidth: 1, fill: '#38BDF8' }} />
          {currentMetricConfig?.targetKey && data.some(d => d[currentMetricConfig.targetKey as keyof PerformanceDataPoint] !== undefined) && (
            <Line type="monotone" dataKey={currentMetricConfig.targetKey} name={`Target ${currentMetricConfig?.label || ''}`} stroke="#F59E0B" strokeWidth={2} strokeDasharray="4 4" dot={false} activeDot={{ r: 5, strokeWidth: 1, fill: '#F59E0B' }}/>
          )}
        </LineChart>
      </ResponsiveContainer>
    );
  };

  return (
    <div className="h-[450px] perspective-racing-input-bg p-4 rounded-lg shadow-md flex flex-col"> {/* Changed background */}
      <div className="mb-4 flex justify-end">
        <select 
          value={selectedMetric} 
          onChange={(e) => setSelectedMetric(e.target.value as ChartMetric)}
          className="appearance-none block perspective-racing-input-bg border perspective-racing-border rounded-md px-3 py-1.5 text-sm focus:ring-sky-500 focus:border-sky-500 h-9 perspective-racing-text-primary"
        >
          {metricOptions.map(opt => (
            <option key={opt.value} value={opt.value} className="perspective-racing-input-bg perspective-racing-text-primary">{opt.label} ({opt.unit})</option>
          ))}
        </select>
      </div>
      <div className="flex-grow">
        {chartContent()}
      </div>
    </div>
  );
};

export default TimeChart;