import React, { useContext, useState, useEffect } from 'react';
import { LiveDataContext } from '../../contexts/LiveDataContext';
import { SailPlan } from '../../types';
import { MOCK_SAIL_OPTIONS } from '../../constants';
import Select from '../ui/Select';
import Button from '../ui/Button';

const SailPlanSelector: React.FC = () => {
  const { sailPlan: currentSailPlan, updateSailPlan, loadingSailPlan, errorSailPlan } = useContext(LiveDataContext);
  const [localSailPlan, setLocalSailPlan] = useState<SailPlan>(currentSailPlan);
  const [isDirty, setIsDirty] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitMessage, setSubmitMessage] = useState<{type: 'success' | 'error', text: string} | null>(null);

  useEffect(() => {
    setLocalSailPlan(currentSailPlan);
    setIsDirty(false); 
  }, [currentSailPlan]);

  const handleChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const { name, value } = e.target;
    setLocalSailPlan(prev => ({ ...prev, [name]: value }));
    setIsDirty(true);
    setSubmitMessage(null); 
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setSubmitMessage(null);
    try {
      await updateSailPlan(localSailPlan);
      setIsDirty(false);
      setSubmitMessage({type: 'success', text: "Sail plan updated successfully!"});
      setTimeout(() => setSubmitMessage(null), 3000);
    } catch (error) {
      setSubmitMessage({type: 'error', text: `Error: ${errorSailPlan || 'Failed to update sail plan.'}`});
    } finally {
      setIsSubmitting(false);
    }
  };

  const headsailOptions = MOCK_SAIL_OPTIONS.HEADSAILS.map(s => ({ value: s, label: s }));
  const spinnakerOptions = MOCK_SAIL_OPTIONS.SPINNAKERS.map(s => ({ value: s, label: s }));
  const reefOptions = MOCK_SAIL_OPTIONS.REEFS.map(s => ({ value: s, label: s }));

  if (loadingSailPlan) {
    return (
      <div className="flex items-center justify-center py-6 perspective-racing-text-secondary">
        <svg className="animate-spin -ml-1 mr-3 h-5 w-5 perspective-racing-accent-sky" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
        Loading sail plan...
      </div>
    );
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-5">
      <Select
        label="Headsail"
        name="headsail"
        id="headsail"
        value={localSailPlan.headsail}
        onChange={handleChange}
        options={headsailOptions}
      />
      <Select
        label="Spinnaker/Reaching Sail"
        name="spinnaker"
        id="spinnaker"
        value={localSailPlan.spinnaker}
        onChange={handleChange}
        options={spinnakerOptions}
      />
      <Select
        label="Reef (Mainsail)"
        name="reef"
        id="reef"
        value={localSailPlan.reef}
        onChange={handleChange}
        options={reefOptions}
      />
      <div className="pt-1">
        <Button type="submit" disabled={!isDirty || isSubmitting} isLoading={isSubmitting} className="w-full">
          {isSubmitting ? 'Updating...' : 'Update Sail Plan'}
        </Button>
      </div>
      {submitMessage && (
        <p className={`text-sm mt-2 p-2 rounded-md ${submitMessage.type === 'error' ? 'bg-red-700/30 text-red-300' : 'bg-green-700/30 text-green-300'}`}>
          {submitMessage.text}
        </p>
      )}
    </form>
  );
};

export default SailPlanSelector;