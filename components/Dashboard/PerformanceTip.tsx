import React, { useContext } from 'react';
import { LiveDataContext } from '../../contexts/LiveDataContext';
import { PERFORMANCE_THRESHOLDS } from '../../constants';

const PerformanceTip: React.FC = () => {
  const { liveData } = useContext(LiveDataContext);

  if (!liveData) {
    return <p className="perspective-racing-text-secondary italic">Awaiting performance data...</p>;
  }

  const { tip, percentPolar, targetVMG, vmg } = liveData;

  let tipColorClass = 'perspective-racing-text-primary';
  if (percentPolar >= PERFORMANCE_THRESHOLDS.EXCELLENT) {
    tipColorClass = 'perspective-racing-accent-teal';
  } else if (percentPolar >= PERFORMANCE_THRESHOLDS.GOOD) {
    tipColorClass = 'text-green-400'; // Keep semantic green
  } else if (percentPolar >= PERFORMANCE_THRESHOLDS.AVERAGE) {
    tipColorClass = 'text-amber-400'; // Keep semantic amber
  } else {
    tipColorClass = 'text-red-400'; // Keep semantic red
  }

  return (
    <div className="p-4 perspective-racing-input-bg rounded-lg shadow"> {/* Changed background */}
      <p className={`text-lg font-semibold ${tipColorClass}`}>{tip || "Keep sailing fast!"}</p>
      <div className="mt-2 text-sm perspective-racing-text-secondary space-y-1">
        <p>Current VMG: <span className="font-medium perspective-racing-text-primary">{vmg.toFixed(2)} kts</span></p>
        <p>Target VMG: <span className="font-medium perspective-racing-text-primary">{targetVMG.toFixed(2)} kts</span></p>
        <p>Polar %: <span className={`font-bold ${tipColorClass}`}>{percentPolar.toFixed(1)}%</span></p>
      </div>
    </div>
  );
};

export default PerformanceTip;