import React, { useContext } from 'react';
import { LiveDataContext } from '../../contexts/LiveDataContext';
import { SeaStateType } from '../../types';
import Badge from '../ui/Badge';

const MetricDisplay: React.FC<{ label: string; value: string | number; unit?: string; className?: string, highlight?: boolean }> = ({ label, value, unit, className = '', highlight }) => (
  <div className={`p-3.5 rounded-lg perspective-racing-input-bg shadow-md hover:bg-slate-700 transition-colors duration-150 ${className}`}> {/* Changed to input-bg for slight contrast */}
    <div className="text-xs perspective-racing-text-secondary uppercase tracking-wider truncate">{label}</div>
    <div className={`mt-1 text-2xl font-bold ${highlight ? 'perspective-racing-accent-sky' : 'perspective-racing-text-primary'}`}>
      {value}
      {unit && <span className="text-sm font-normal ml-1.5 perspective-racing-text-secondary">{unit}</span>}
    </div>
  </div>
);

const getSeaStateBadgeColor = (seaState: SeaStateType): 'green' | 'amber' | 'red' | 'sky' => {
  switch (seaState) {
    case SeaStateType.CALM: return 'green';
    case SeaStateType.MODERATE: return 'amber';
    case SeaStateType.ROUGH: return 'red';
    case SeaStateType.VERY_ROUGH: return 'red';
    default: return 'sky';
  }
};

const LiveMetrics: React.FC = () => {
  const { liveData } = useContext(LiveDataContext);

  if (!liveData) {
    return (
      <div className="flex items-center justify-center py-10 perspective-racing-text-secondary">
        <svg className="animate-spin -ml-1 mr-3 h-5 w-5 perspective-racing-accent-sky" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
        Waiting for live data...
      </div>
    );
  }

  return (
    <div className="space-y-5">
      <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 gap-4">
        <MetricDisplay label="Boat Speed (BSP)" value={liveData.bsp.toFixed(1)} unit="kts" highlight />
        <MetricDisplay label="VMG" value={liveData.vmg.toFixed(1)} unit="kts" highlight />
        <MetricDisplay label="True Wind Speed (TWS)" value={liveData.tws.toFixed(1)} unit="kts" />
        <MetricDisplay label="True Wind Angle (TWA)" value={liveData.twa.toFixed(0)} unit="°" />
        
        <MetricDisplay label="Apparent Wind (AWS)" value={liveData.aws?.toFixed(1) ?? 'N/A'} unit="kts" />
        <MetricDisplay label="Apparent Angle (AWA)" value={liveData.awa?.toFixed(0) ?? 'N/A'} unit="°" />
        <MetricDisplay label="Heading (True)" value={liveData.heading?.toFixed(0) ?? 'N/A'} unit="°" />
        <MetricDisplay label="SOG" value={liveData.sog?.toFixed(1) ?? 'N/A'} unit="kts" />
        
        <MetricDisplay label="COG" value={liveData.cog?.toFixed(0) ?? 'N/A'} unit="°" />
        <MetricDisplay label="Heel" value={liveData.heel.toFixed(1)} unit="°" />
        <MetricDisplay label="Pitch" value={liveData.pitch.toFixed(1)} unit="°" />

        <div className="p-3.5 rounded-lg perspective-racing-input-bg shadow-md hover:bg-slate-700 transition-colors duration-150">
          <div className="text-xs perspective-racing-text-secondary uppercase tracking-wider">Sea State</div>
          <div className="mt-1.5">
            <Badge color={getSeaStateBadgeColor(liveData.seaState)} size="md">{liveData.seaState}</Badge>
          </div>
        </div>
        
        <MetricDisplay label="Depth" value={liveData.depth?.toFixed(1) ?? 'N/A'} unit="m" className="lg:col-start-4 lg:row-start-3" />
      </div>
      <div className="text-xs text-slate-500 text-right pt-2">Last updated: {new Date(liveData.utcTime).toLocaleTimeString()}</div>
    </div>
  );
};

export default LiveMetrics;