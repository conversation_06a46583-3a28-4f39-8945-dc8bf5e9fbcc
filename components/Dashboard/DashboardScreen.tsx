
import React from 'react';
import LiveMetrics from './LiveMetrics';
import SailPlanSelector from './SailPlanSelector';
import PerformanceTip from './PerformanceTip';
import Card from '../ui/Card';

const DashboardScreen: React.FC = () => {
  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <Card title="Live Metrics" className="lg:col-span-2">
          <LiveMetrics />
        </Card>
        <Card title="Sail Configuration">
          <SailPlanSelector />
        </Card>
      </div>
      <Card title="Performance Analysis">
        <PerformanceTip />
      </Card>
      {/* Additional dashboard widgets can be added here */}
    </div>
  );
};

export default DashboardScreen;
