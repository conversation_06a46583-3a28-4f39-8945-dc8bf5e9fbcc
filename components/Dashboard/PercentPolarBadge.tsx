
import React, { useContext } from 'react';
import { LiveDataContext } from '../../contexts/LiveDataContext';
import { PERFORMANCE_THRESHOLDS } from '../../constants';
import Badge from '../ui/Badge';

const PercentPolarBadge: React.FC = () => {
  const { liveData } = useContext(LiveDataContext);

  if (!liveData) {
    return (
      <div className="px-3 py-1.5 text-xs font-semibold rounded-full bg-gray-700 text-gray-300">
        -- %
      </div>
    );
  }

  const { percentPolar } = liveData;
  let color: 'teal' | 'green' | 'amber' | 'red' = 'red';

  if (percentPolar >= PERFORMANCE_THRESHOLDS.EXCELLENT) {
    color = 'teal';
  } else if (percentPolar >= PERFORMANCE_THRESHOLDS.GOOD) {
    color = 'green';
  } else if (percentPolar >= PERFORMANCE_THRESHOLDS.AVERAGE) {
    color = 'amber';
  }

  return (
    <div className="flex items-center space-x-2">
      <span className="text-xs text-gray-400 hidden sm:inline">POLAR:</span>
      <Badge color={color} size="md" className="text-lg font-bold px-3 py-1">
        {percentPolar.toFixed(0)}%
      </Badge>
    </div>
  );
};

export default PercentPolarBadge;
