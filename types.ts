
export interface LiveData {
  bsp: number; // Boat Speed (knots)
  tws: number; // True Wind Speed (knots)
  twa: number; // True Wind Angle (degrees)
  vmg: number; // Velocity Made Good (knots)
  seaState: SeaStateType; // Sea State (e.g., 'Calm', 'Moderate', 'Rough')
  targetVMG: number; // Target VMG from polars (knots)
  percentPolar: number; // Performance as % of polar VMG
  tip: string; // Performance tip (e.g., "Too low angle", "Need more trim", "On target")
  heel: number; // Heel angle (degrees)
  pitch: number; // Pitch angle (degrees)
  roll: number; // Roll angle (degrees)
  utcTime: string; // UTC time string
  lat?: number;
  lon?: number;
  heading?: number; // True heading
  sog?: number; // Speed Over Ground
  cog?: number; // Course Over Ground
  depth?: number; // Depth in meters
  aws?: number; // Apparent Wind Speed
  awa?: number; // Apparent Wind Angle
}

export enum SeaStateType {
  CALM = 'Calm',
  MODERATE = 'Moderate',
  ROUGH = 'Rough',
  VERY_ROUGH = 'Very Rough',
}

export interface SailPlan {
  headsail: string;
  spinnaker: string;
  reef: string; // e.g., 'None', 'Reef 1', 'Reef 2'
}

export interface PolarEntry {
  tws: number;
  twa: number;
  targetSpeed: number; // Target boat speed for this TWS/TWA
  sailConfiguration?: string; // Optional: e.g. 'J1, A2'
}

export interface PolarTable {
  name: string;
  description?: string;
  entries: PolarEntry[];
}

export interface RaceEvent {
  id: string;
  name: string;
  startTime: Date;
  endTime?: Date;
  route?: Array<{ lat: number; lon: number; name?: string }>; // Array of waypoints
  notes?: string;
}

export interface CrewMember {
  id: string;
  name: string;
  role?: string;
}

export interface CrewRSVP {
  raceId: string;
  crewMemberId: string;
  status: 'Attending' | 'Not Attending' | 'Maybe';
}

export interface PerformanceDataPoint extends LiveData {
  timestamp: number;
}

export interface TackJibeInfo {
  id: string;
  type: 'Tack' | 'Jibe';
  startTime: number; // timestamp
  endTime: number; // timestamp
  durationSeconds: number;
  distanceLostMeters?: number; // Optional
  entrySpeed: number;
  exitSpeed: number;
  avgSpeedDuringManeuver: number;
}
