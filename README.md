# Perspective Racing

A modern sailing analytics application built with React and TypeScript.

## Real Data Integration

This application has been configured to use real data sources instead of demo/mock data. To fully utilize the application, you'll need to set up the following:

### Environment Variables

Create a `.env` file in the root directory with the following variables:

```
REACT_APP_API_URL=http://localhost:3001/api
REACT_APP_WS_URL=ws://localhost:3001
```

### Backend API Requirements

The application expects a REST API with the following endpoints:

#### Sail Plan
- `GET /api/sailplan` - Get current sail plan
- `PUT /api/sailplan` - Update sail plan

#### Polar Tables
- `GET /api/polars` - Get all polar tables
- `POST /api/polars` - Create new polar table
- `PUT /api/polars/:name` - Update polar table
- `DELETE /api/polars/:name` - Delete polar table

#### Race Events
- `GET /api/races` - Get all race events
- `POST /api/races` - Create new race event
- `PUT /api/races/:id` - Update race event
- `DELETE /api/races/:id` - Delete race event

#### Crew Management
- `GET /api/crew` - Get all crew members
- `GET /api/races/:id/rsvps` - Get RSVPs for a race
- `PUT /api/races/:id/rsvps` - Update RSVP

#### Performance Data
- `GET /api/races/:id/summary` - Get performance summary for a race
- `GET /api/races/:id/performance` - Get time series performance data
- `GET /api/races/:id/maneuvers` - Get tack/jibe analysis data

### WebSocket Live Data

The application connects to a WebSocket server for real-time sailing data. The WebSocket should send JSON messages with the following structure:

```json
{
  "bsp": 8.5,
  "tws": 12.3,
  "twa": 45,
  "vmg": 6.2,
  "seaState": "Moderate",
  "targetVMG": 6.5,
  "percentPolar": 95.4,
  "tip": "Perfect angle, keep it up!",
  "heel": 12.5,
  "pitch": 2.1,
  "roll": -1.3,
  "utcTime": "2024-01-15T14:30:00.000Z",
  "lat": 34.0522,
  "lon": -118.2437,
  "heading": 285,
  "sog": 8.3,
  "cog": 287,
  "depth": 15.2,
  "aws": 13.1,
  "awa": 42
}
```

### No Data States

When no backend is available or no data exists, the application will:
- Show empty states with appropriate messages
- Display loading indicators while attempting to connect
- Gracefully handle API failures with fallback behavior
- Allow manual configuration of sail plans and polar tables

## Development

```bash
npm install
npm run dev
```

The application will run on `http://localhost:5173` and attempt to connect to the configured API and WebSocket endpoints.
