<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Perspective Racing</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/react@18/umd/react.production.min.js" crossorigin></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.production.min.js" crossorigin></script>
    <script src="https://unpkg.com/recharts@2.12.7/umd/Recharts.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js" integrity="sha512-BNaRQnYJYiPSqHHDb58B0yaPfCu+Wgds8Gp/gU33kqBtgNS4tSPHuGibyoVBl5gDODEEPNIhJfYurLMyMFStg==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    
    <!-- Leaflet.js CSS -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css"
      integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY="
      crossorigin=""/>
    <!-- Leaflet.js JavaScript -->
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"
      integrity="sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo="
      crossorigin=""></script>

    <link rel="icon" href="data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 viewBox=%220 0 100 100%22><text y=%22.9em%22 font-size=%2290%22>⛵</text></svg>">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <style>
      /* Ensure recharts tooltips are visible */
      .recharts-tooltip-wrapper {
        z-index: 9999 !important;
      }
      body {
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        background-color: #0A0E1A; /* Dark Slate Blue */
        color: #E0E0E0; /* Off-white */
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
      }
      /* Custom scrollbar for webkit browsers */
      ::-webkit-scrollbar {
        width: 8px;
        height: 8px;
      }
      ::-webkit-scrollbar-track {
        background: #131A2A; /* Slightly lighter slate blue */
        border-radius: 4px;
      }
      ::-webkit-scrollbar-thumb {
        background: #38BDF8; /* Sky accent */
        border-radius: 4px;
        /* border: 2px solid #131A2A; */
      }
      ::-webkit-scrollbar-thumb:hover {
        background: #0EA5E9; /* Darker sky accent */
      }
      /* For Firefox */
      * {
        scrollbar-width: thin;
        scrollbar-color: #38BDF8 #131A2A; /* thumb track */
      }
      .perspective-racing-app-bg { background-color: #0A0E1A; }
      .perspective-racing-card-bg { background-color: #131A2A; }
      .perspective-racing-input-bg { background-color: #1E293B; }
      .perspective-racing-border { border-color: #334155; }
      .perspective-racing-text-primary { color: #E0E0E0; }
      .perspective-racing-text-secondary { color: #94A3B8; }
      .perspective-racing-accent-sky { color: #38BDF8; }
      .perspective-racing-accent-teal { color: #2DD4BF; }
      .perspective-racing-focus-ring { ring-color: #38BDF8; }

      /* Leaflet map container needs explicit height */
      .leaflet-container {
        height: 100%; /* Ensure map fills its container */
        width: 100%;
        border-radius: 0.5rem; /* match card rounding */
      }
    </style>
  <script type="importmap">
{
  "imports": {
    "react-dom/": "https://esm.sh/react-dom@^19.1.0/",
    "react/": "https://esm.sh/react@^19.1.0/",
    "react": "https://esm.sh/react@^19.1.0",
    "react-router-dom": "https://esm.sh/react-router-dom@^7.6.1"
  }
}
</script>
</head>
  <body class="perspective-racing-app-bg perspective-racing-text-primary">
    <div id="root"></div>
    <script type="module" src="/index.tsx"></script>
  </body>
</html><link rel="stylesheet" href="index.css">
<script src="index.tsx" type="module"></script>
